<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Slide-In Modal System</title>
    <!-- Adobe Clean Font -->
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Adobe CEP Inspired Styles */
        :root {
            --cep-bg-dark: #2A2A2A;
            --cep-panel-dark: #363636;
            --cep-border-dark: #4D4D4D;
            --cep-text-primary: #F0F0F0;
            --cep-text-secondary: #B3B3B3;
            --cep-blue: #0078D7;
            --cep-green: #00B294;
            --cep-yellow: #FFB900;
            --cep-red: #D83B01;
            --cep-highlight: rgba(255, 255, 255, 0.05);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Source Sans Pro', sans-serif;
            background-color: var(--cep-bg-dark);
            color: var(--cep-text-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
            font-weight: 400;
            line-height: 1.5;
        }

        /* Slide-In Modal Container */
        .slide-in-modal {
            position: fixed;
            top: 0;
            right: -500px;
            width: 500px;
            height: 100vh;
            background-color: var(--cep-panel-dark);
            border-left: 1px solid var(--cep-border-dark);
            box-shadow: -5px 0 15px rgba(0, 0, 0, 0.3);
            transition: right 0.3s ease-out;
            z-index: 1000;
            display: flex;
            flex-direction: column;
        }

        .slide-in-modal.open {
            right: 0;
        }

        /* Modal Header */
        .slide-modal-header {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--cep-border-dark);
        }

        .slide-modal-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--cep-text-primary);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            font-size: 14px;
        }

        .breadcrumb-link {
            color: var(--cep-blue);
            background: none;
            border: none;
            cursor: pointer;
            padding: 0;
            font-size: 14px;
        }

        .breadcrumb-link:hover {
            text-decoration: underline;
        }

        .breadcrumb-separator {
            margin: 0 8px;
            color: var(--cep-text-secondary);
        }

        .breadcrumb-current {
            color: var(--cep-text-primary);
        }

        .slide-close-button {
            background: none;
            border: none;
            color: var(--cep-text-secondary);
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            line-height: 1;
        }

        .slide-close-button:hover {
            color: var(--cep-text-primary);
        }

        /* Modal Body */
        .slide-modal-body {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        /* Settings Panel */
        .settings-section {
            margin-bottom: 25px;
        }

        .settings-section-title {
            font-size: 14px;
            font-weight: 600;
            color: var(--cep-text-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--cep-border-dark);
        }

        .settings-link {
            display: flex;
            align-items: center;
            width: 100%;
            padding: 12px;
            margin-bottom: 8px;
            background-color: transparent;
            border: none;
            border-radius: 4px;
            color: var(--cep-text-primary);
            text-align: left;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .settings-link:hover {
            background-color: var(--cep-highlight);
        }

        .settings-icon {
            font-size: 20px;
            margin-right: 12px;
            width: 24px;
            text-align: center;
        }

        .settings-link-text {
            flex: 1;
        }

        .settings-link-description {
            font-size: 12px;
            color: var(--cep-text-secondary);
            margin-top: 4px;
        }

        /* Sub-Modal Content */
        .sub-modal-content {
            padding: 10px 0;
        }

        /* Demo Content for Sub-Modals */
        .demo-modal-content {
            padding: 20px;
            background-color: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            border: 1px solid var(--cep-border-dark);
            margin-bottom: 20px;
        }

        .demo-modal-title {
            font-size: 16px;
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--cep-text-primary);
        }

        .demo-modal-text {
            font-size: 14px;
            color: var(--cep-text-secondary);
            margin-bottom: 15px;
        }

        /* Toggle Button for Demo */
        .toggle-modal-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 20px;
            background-color: var(--cep-blue);
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: 600;
            z-index: 100;
        }

        /* Hidden state */
        .hidden {
            display: none;
        }

        /* Scrollbar styling */
        .slide-modal-body::-webkit-scrollbar {
            width: 6px;
        }

        .slide-modal-body::-webkit-scrollbar-track {
            background: transparent;
        }

        .slide-modal-body::-webkit-scrollbar-thumb {
            background: var(--cep-border-dark);
            border-radius: 3px;
        }

        .slide-modal-body::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <!-- Toggle Button for Demo -->
    <button class="toggle-modal-button" id="toggleModal">Open Settings</button>

    <!-- Slide-In Modal -->
    <div class="slide-in-modal" id="slideInModal">
        <!-- Settings Panel -->
        <div class="settings-panel" id="settingsPanel">
            <div class="slide-modal-header">
                <span class="slide-modal-title">Settings</span>
                <button class="slide-close-button" id="closeButton">×</button>
            </div>
            <div class="slide-modal-body">
                <!-- Core Features Section -->
                <div class="settings-section">
                    <h3 class="settings-section-title">Core Features</h3>
                    <button class="settings-link" data-modal="analytics">
                        <span class="settings-icon">📊</span>
                        <div class="settings-link-text">
                            <div>Analytics</div>
                            <div class="settings-link-description">View usage statistics and performance metrics</div>
                        </div>
                    </button>
                    <button class="settings-link" data-modal="advancedConfig">
                        <span class="settings-icon">⚙️</span>
                        <div class="settings-link-text">
                            <div>Advanced Config</div>
                            <div class="settings-link-description">Configure advanced settings and preferences</div>
                        </div>
                    </button>
                </div>

                <!-- Model Tools Section -->
                <div class="settings-section">
                    <h3 class="settings-section-title">Model Tools</h3>
                    <button class="settings-link" data-modal="modelComparison">
                        <span class="settings-icon">🔄</span>
                        <div class="settings-link-text">
                            <div>Model Comparison</div>
                            <div class="settings-link-description">Compare different AI models side by side</div>
                        </div>
                    </button>
                    <button class="settings-link" data-modal="multiModel">
                        <span class="settings-icon">🤖</span>
                        <div class="settings-link-text">
                            <div>Multi-Model Chat</div>
                            <div class="settings-link-description">Chat with multiple models simultaneously</div>
                        </div>
                    </button>
                </div>

                <!-- Help & Support Section -->
                <div class="settings-section">
                    <h3 class="settings-section-title">Help & Support</h3>
                    <button class="settings-link" data-modal="help">
                        <span class="settings-icon">❓</span>
                        <div class="settings-link-text">
                            <div>Help</div>
                            <div class="settings-link-description">Get help and view documentation</div>
                        </div>
                    </button>
                    <button class="settings-link" data-modal="about">
                        <span class="settings-icon">ℹ️</span>
                        <div class="settings-link-text">
                            <div>About</div>
                            <div class="settings-link-description">About SahAI CEP Extension</div>
                        </div>
                    </button>
                </div>
            </div>
        </div>

        <!-- Sub-Modal Panel -->
        <div class="sub-modal-panel hidden" id="subModalPanel">
            <div class="slide-modal-header">
                <div class="breadcrumb">
                    <button class="breadcrumb-link" id="backButton">Settings</button>
                    <span class="breadcrumb-separator">›</span>
                    <span class="breadcrumb-current" id="currentModalTitle">Modal</span>
                </div>
                <button class="slide-close-button" id="subModalCloseButton">×</button>
            </div>
            <div class="slide-modal-body">
                <div class="sub-modal-content" id="subModalContent">
                    <!-- Content will be dynamically inserted here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // DOM Elements
        const slideInModal = document.getElementById('slideInModal');
        const toggleModalButton = document.getElementById('toggleModal');
        const closeButton = document.getElementById('closeButton');
        const settingsPanel = document.getElementById('settingsPanel');
        const subModalPanel = document.getElementById('subModalPanel');
        const backButton = document.getElementById('backButton');
        const subModalCloseButton = document.getElementById('subModalCloseButton');
        const currentModalTitle = document.getElementById('currentModalTitle');
        const subModalContent = document.getElementById('subModalContent');

        // Modal content templates
        const modalContents = {
            analytics: `
                <div class="demo-modal-content">
                    <h3 class="demo-modal-title">Analytics Dashboard</h3>
                    <p class="demo-modal-text">View usage statistics and performance metrics for your AI models.</p>
                    <p class="demo-modal-text">This panel would show graphs and charts of your usage data.</p>
                </div>
            `,
            advancedConfig: `
                <div class="demo-modal-content">
                    <h3 class="demo-modal-title">Advanced Configuration</h3>
                    <p class="demo-modal-text">Configure advanced settings and preferences for the extension.</p>
                    <p class="demo-modal-text">This would include technical settings and customization options.</p>
                </div>
            `,
            modelComparison: `
                <div class="demo-modal-content">
                    <h3 class="demo-modal-title">Model Comparison</h3>
                    <p class="demo-modal-text">Compare different AI models side by side to evaluate their performance.</p>
                    <p class="demo-modal-text">This would show a table comparing response quality, speed, and other metrics.</p>
                </div>
            `,
            multiModel: `
                <div class="demo-modal-content">
                    <h3 class="demo-modal-title">Multi-Model Chat</h3>
                    <p class="demo-modal-text">Chat with multiple models simultaneously to compare responses.</p>
                    <p class="demo-modal-text">This would show multiple chat windows side by side.</p>
                </div>
            `,
            help: `
                <div class="demo-modal-content">
                    <h3 class="demo-modal-title">Help & Documentation</h3>
                    <p class="demo-modal-text">Get help and view documentation for the SahAI CEP Extension.</p>
                    <p class="demo-modal-text">This would include FAQs, tutorials, and support information.</p>
                </div>
            `,
            about: `
                <div class="demo-modal-content">
                    <h3 class="demo-modal-title">About SahAI</h3>
                    <p class="demo-modal-text">Version 2.0.0</p>
                    <p class="demo-modal-text">© 2023 SahAI Technologies</p>
                    <p class="demo-modal-text">All rights reserved.</p>
                </div>
            `
        };

        // Toggle modal visibility
        toggleModalButton.addEventListener('click', () => {
            slideInModal.classList.toggle('open');
            toggleModalButton.textContent = slideInModal.classList.contains('open') ? 'Close Settings' : 'Open Settings';
        });

        // Close modal
        closeButton.addEventListener('click', () => {
            slideInModal.classList.remove('open');
            toggleModalButton.textContent = 'Open Settings';
        });

        // Handle settings link clicks
        document.querySelectorAll('.settings-link').forEach(link => {
            link.addEventListener('click', () => {
                const modalType = link.getAttribute('data-modal');
                currentModalTitle.textContent = link.querySelector('.settings-link-text div').textContent;
                subModalContent.innerHTML = modalContents[modalType];
                
                settingsPanel.classList.add('hidden');
                subModalPanel.classList.remove('hidden');
            });
        });

        // Back to settings
        backButton.addEventListener('click', () => {
            settingsPanel.classList.remove('hidden');
            subModalPanel.classList.add('hidden');
        });

        // Close sub-modal
        subModalCloseButton.addEventListener('click', () => {
            slideInModal.classList.remove('open');
            toggleModalButton.textContent = 'Open Settings';
        });

        // Handle Escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape') {
                if (!subModalPanel.classList.contains('hidden')) {
                    settingsPanel.classList.remove('hidden');
                    subModalPanel.classList.add('hidden');
                } else {
                    slideInModal.classList.remove('open');
                    toggleModalButton.textContent = 'Open Settings';
                }
            }
        });
    </script>
</body>
</html>