<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Help & Support</title>
    <!-- Adobe Clean Font -->
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Adobe CEP Inspired Styles */
        :root {
            --cep-bg-dark: #2A2A2A;
            --cep-panel-dark: #363636;
            --cep-border-dark: #4D4D4D;
            --cep-text-primary: #F0F0F0;
            --cep-text-secondary: #B3B3B3;
            --cep-blue: #0078D7;
            --cep-green: #00B294;
            --cep-yellow: #FFB900;
            --cep-red: #D83B01;
            --cep-highlight: rgba(255, 255, 255, 0.05);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Source Sans Pro', sans-serif;
            background-color: var(--cep-bg-dark);
            color: var(--cep-text-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* Modal Container */
        .modal-container {
            width: 100%;
            max-width: 600px;
            height: 700px;
            background-color: var(--cep-panel-dark);
            border-radius: 4px;
            border: 1px solid var(--cep-border-dark);
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }

        /* Modal Header */
        .modal-header {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--cep-border-dark);
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
        }

        .modal-close-button {
            background: none;
            border: none;
            color: var(--cep-text-secondary);
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            line-height: 1;
        }

        .modal-close-button:hover {
            color: var(--cep-text-primary);
        }

        /* Modal Content */
        .modal-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
        }

        /* Help Sections */
        .help-section {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .section-header {
            font-size: 18px;
            font-weight: 600;
            color: var(--cep-text-primary);
            margin-bottom: 10px;
        }

        .section-description {
            font-size: 14px;
            color: var(--cep-text-secondary);
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .subsection-header {
            font-size: 16px;
            font-weight: 600;
            color: var(--cep-text-primary);
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 1px solid var(--cep-border-dark);
        }

        /* Quick Start Guide */
        .steps-list {
            padding-left: 20px;
            margin-bottom: 20px;
        }

        .steps-list li {
            margin-bottom: 10px;
            font-size: 14px;
            line-height: 1.5;
        }

        .steps-list strong {
            color: var(--cep-blue);
        }

        /* FAQ Section */
        .faq-list {
            display: flex;
            flex-direction: column;
            gap: 10px;
            margin-bottom: 20px;
        }

        .faq-item {
            border: 1px solid var(--cep-border-dark);
            border-radius: 4px;
            overflow: hidden;
        }

        .faq-question {
            width: 100%;
            padding: 12px 15px;
            background-color: rgba(0, 0, 0, 0.15);
            border: none;
            color: var(--cep-text-primary);
            text-align: left;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 14px;
            transition: background-color 0.2s ease;
        }

        .faq-question:hover {
            background-color: var(--cep-highlight);
        }

        .faq-toggle {
            font-size: 18px;
            font-weight: bold;
            margin-left: 10px;
            color: var(--cep-blue);
        }

        .faq-answer {
            padding: 15px;
            background-color: rgba(0, 0, 0, 0.1);
            font-size: 13px;
            color: var(--cep-text-secondary);
            line-height: 1.5;
            border-top: 1px solid var(--cep-border-dark);
        }

        /* Support Section */
        .support-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
        }

        .support-button {
            padding: 10px 12px;
            background-color: rgba(0, 120, 215, 0.2);
            border: 1px solid var(--cep-blue);
            border-radius: 3px;
            color: var(--cep-text-primary);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: left;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .support-button:hover {
            background-color: rgba(0, 120, 215, 0.3);
        }

        /* Tips Section */
        .tips-list {
            list-style: none;
            padding-left: 0;
        }

        .tip-item {
            margin-bottom: 10px;
            font-size: 14px;
            line-height: 1.5;
            padding-left: 20px;
            position: relative;
        }

        .tip-item::before {
            content: "•";
            color: var(--cep-blue);
            font-weight: bold;
            position: absolute;
            left: 5px;
        }

        .tip-item strong {
            color: var(--cep-blue);
        }

        /* Scrollbar styling */
        .modal-content::-webkit-scrollbar {
            width: 6px;
        }

        .modal-content::-webkit-scrollbar-track {
            background: transparent;
        }

        .modal-content::-webkit-scrollbar-thumb {
            background: var(--cep-border-dark);
            border-radius: 3px;
        }

        .modal-content::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="modal-container">
        <div class="modal-header">
            <span class="modal-title">Help & Support</span>
            <button class="modal-close-button" id="closeButton">×</button>
        </div>
        
        <div class="modal-content">
            <div class="help-section">
                <h3 class="section-header">Help & Support</h3>
                <p class="section-description">
                    Find quick answers and guides to get the most out of SahAI.
                </p>

                <!-- Quick Start Guide -->
                <div class="quick-start">
                    <h4 class="subsection-header">Quick Start Guide</h4>
                    <ol class="steps-list">
                        <li><strong>Configure Provider:</strong> Go to Settings and enter your API key.</li>
                        <li><strong>Select Model:</strong> Choose your preferred AI model from the dropdown.</li>
                        <li><strong>Start Chatting:</strong> Type your query and get instant AI responses.</li>
                        <li><strong>Explore Features:</strong> Check out analytics and multi-model chat.</li>
                    </ol>
                </div>

                <!-- FAQ Section -->
                <div class="faq-section">
                    <h4 class="subsection-header">Frequently Asked Questions</h4>
                    <div class="faq-list">
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFAQ(this)">
                                <span>How do I set up API keys for AI providers?</span>
                                <span class="faq-toggle">+</span>
                            </button>
                            <div class="faq-answer">
                                Go to Settings > Advanced Config to configure API keys for different AI providers. Your keys are stored securely and encrypted locally.
                            </div>
                        </div>
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFAQ(this)">
                                <span>Which Adobe applications are supported?</span>
                                <span class="faq-toggle">+</span>
                            </button>
                            <div class="faq-answer">
                                SahAI supports After Effects 2025, Premiere Pro 2025, Photoshop 2025, and Illustrator 2025. Make sure you have the latest versions installed.
                            </div>
                        </div>
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFAQ(this)">
                                <span>How do I compare different AI models?</span>
                                <span class="faq-toggle">+</span>
                            </button>
                            <div class="faq-answer">
                                Use the Model Comparison feature from the settings menu to test different models side by side and see their performance metrics.
                            </div>
                        </div>
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFAQ(this)">
                                <span>Can I use multiple AI models simultaneously?</span>
                                <span class="faq-toggle">+</span>
                            </button>
                            <div class="faq-answer">
                                Yes! The Multi-Model Chat feature allows you to chat with multiple AI models in one conversation thread for better results.
                            </div>
                        </div>
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFAQ(this)">
                                <span>How do I troubleshoot connection issues?</span>
                                <span class="faq-toggle">+</span>
                            </button>
                            <div class="faq-answer">
                                Check your internet connection, verify your API keys are correct, and ensure the AI provider services are operational. Check the Analytics panel for detailed error logs.
                            </div>
                        </div>
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFAQ(this)">
                                <span>Is my data secure?</span>
                                <span class="faq-toggle">+</span>
                            </button>
                            <div class="faq-answer">
                                Yes, all API keys are encrypted and stored locally. Your conversations are not stored permanently and are only sent to the AI providers you've configured.
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Support Section -->
                <div class="support-section">
                    <h3 class="subsection-header">Need More Help?</h3>
                    <div class="support-grid">
                        <button class="support-button" onclick="window.open('https://github.com/sahai-ai/cep-extension/wiki', '_blank')">
                            📖 Full Documentation
                        </button>
                        <button class="support-button" onclick="window.open('https://github.com/sahai-ai/cep-extension/discussions', '_blank')">
                            💬 Community Discussions
                        </button>
                        <button class="support-button" onclick="window.open('https://github.com/sahai-ai/cep-extension/issues/new', '_blank')">
                            🐛 Report a Bug
                        </button>
                        <button class="support-button" onclick="window.open('mailto:<EMAIL>', '_blank')">
                            ✉️ Contact Support
                        </button>
                    </div>
                </div>

                <!-- Pro Tips Section -->
                <div class="tips-section">
                    <h4 class="subsection-header">Pro Tips</h4>
                    <ul class="tips-list">
                        <li class="tip-item">
                            <strong>Keyboard Shortcuts:</strong> Use Ctrl+Enter to send messages quickly.
                        </li>
                        <li class="tip-item">
                            <strong>Contextual Help:</strong> Hover over elements for tooltips and more information.
                        </li>
                        <li class="tip-item">
                            <strong>Experiment:</strong> Try different models for varied response styles.
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Close button functionality
        document.getElementById('closeButton').addEventListener('click', function() {
            console.log('Modal closed');
        });

        // FAQ toggle functionality
        function toggleFAQ(button) {
            const faqItem = button.closest('.faq-item');
            const answer = faqItem.querySelector('.faq-answer');
            const toggle = faqItem.querySelector('.faq-toggle');
            
            if (answer.style.display === 'block') {
                answer.style.display = 'none';
                toggle.textContent = '+';
            } else {
                answer.style.display = 'block';
                toggle.textContent = '−';
            }
        }

        // Initialize all FAQ answers as hidden
        document.querySelectorAll('.faq-answer').forEach(answer => {
            answer.style.display = 'none';
        });
    </script>
</body>
</html>