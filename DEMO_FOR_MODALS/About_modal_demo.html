<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About SahAI Extension</title>
    <!-- Adobe Clean Font -->
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Adobe CEP Inspired Styles */
        :root {
            --cep-bg-dark: #2A2A2A;
            --cep-panel-dark: #363636;
            --cep-border-dark: #4D4D4D;
            --cep-text-primary: #F0F0F0;
            --cep-text-secondary: #B3B3B3;
            --cep-blue: #0078D7;
            --cep-green: #00B294;
            --cep-yellow: #FFB900;
            --cep-red: #D83B01;
            --cep-highlight: rgba(255, 255, 255, 0.05);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Source Sans Pro', sans-serif;
            background-color: var(--cep-bg-dark);
            color: var(--cep-text-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* Modal Container */
        .modal-container {
            width: 100%;
            max-width: 500px;
            background-color: var(--cep-panel-dark);
            border-radius: 4px;
            border: 1px solid var(--cep-border-dark);
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        /* Modal Header */
        .modal-header {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--cep-border-dark);
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
        }

        .modal-close-button {
            background: none;
            border: none;
            color: var(--cep-text-secondary);
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            line-height: 1;
        }

        .modal-close-button:hover {
            color: var(--cep-text-primary);
        }

        /* Modal Content */
        .modal-content {
            padding: 20px;
        }

        /* About Sections */
        .about-section {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        /* Logo Section */
        .logo-section {
            text-align: center;
            padding: 15px 0;
        }

        .app-icon {
            font-size: 48px;
            margin-bottom: 10px;
        }

        .app-name {
            font-size: 22px;
            font-weight: 600;
            margin-bottom: 5px;
            color: var(--cep-text-primary);
        }

        .version {
            font-size: 14px;
            color: var(--cep-text-secondary);
        }

        /* Info Section */
        .info-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .section-header {
            font-size: 16px;
            font-weight: 600;
            color: var(--cep-text-primary);
            margin-bottom: 5px;
        }

        .section-description {
            font-size: 14px;
            color: var(--cep-text-secondary);
            line-height: 1.5;
        }

        /* Features List */
        .features-list {
            list-style: none;
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 10px;
        }

        .feature-item {
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .feature-icon {
            font-size: 20px;
            flex-shrink: 0;
        }

        .feature-item div {
            font-size: 14px;
            color: var(--cep-text-primary);
        }

        .feature-item strong {
            color: var(--cep-blue);
        }

        /* Links Section */
        .links-section {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .link-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }

        .link-button {
            padding: 8px 12px;
            background-color: rgba(0, 120, 215, 0.2);
            border: 1px solid var(--cep-blue);
            border-radius: 3px;
            color: var(--cep-text-primary);
            font-size: 13px;
            cursor: pointer;
            transition: all 0.2s ease;
            text-align: center;
        }

        .link-button:hover {
            background-color: rgba(0, 120, 215, 0.3);
        }

        /* Copyright Section */
        .copyright-section {
            margin-top: 20px;
            text-align: center;
            padding-top: 15px;
            border-top: 1px solid var(--cep-border-dark);
        }

        .copyright {
            font-size: 12px;
            color: var(--cep-text-secondary);
            margin-bottom: 5px;
        }

        .disclaimer {
            font-size: 11px;
            color: var(--cep-text-secondary);
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="modal-container">
        <div class="modal-header">
            <span class="modal-title">About SahAI Extension</span>
            <button class="modal-close-button" id="closeButton">×</button>
        </div>
        
        <div class="modal-content">
            <div class="about-section">
                <!-- Logo Section -->
                <div class="logo-section">
                    <div class="app-icon">✨</div>
                    <h2 class="app-name">SahAI Extension</h2>
                    <p class="version">Version 1.0.0</p>
                </div>

                <!-- Info Section -->
                <div class="info-section">
                    <h3 class="section-header">About This Extension</h3>
                    <p class="section-description">
                        SahAI is a powerful AI extension designed to enhance your creative workflow.
                        It integrates seamlessly with your environment to provide intelligent assistance.
                    </p>

                    <!-- Features List -->
                    <ul class="features-list">
                        <li class="feature-item">
                            <span class="feature-icon">🚀</span>
                            <div>
                                <strong>Multi-Model Support:</strong> Access various AI models.
                            </div>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">📊</span>
                            <div>
                                <strong>Usage Analytics:</strong> Track your AI interactions.
                            </div>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">⚙️</span>
                            <div>
                                <strong>Advanced Configuration:</strong> Tailor settings to your needs.
                            </div>
                        </li>
                        <li class="feature-item">
                            <span class="feature-icon">📚</span>
                            <div>
                                <strong>Comprehensive Help:</strong> Get started quickly.
                            </div>
                        </li>
                    </ul>
                </div>

                <!-- Links Section -->
                <div class="links-section">
                    <h3 class="section-header">Useful Links</h3>
                    <div class="link-grid">
                        <button class="link-button">Documentation</button>
                        <button class="link-button">GitHub Repository</button>
                        <button class="link-button">Privacy Policy</button>
                        <button class="link-button">Terms of Service</button>
                    </div>
                </div>

                <!-- Copyright Section -->
                <div class="copyright-section">
                    <p class="copyright">© 2025 SahAI Extension. All rights reserved.</p>
                    <p class="disclaimer">This software is provided "as is," without warranty of any kind.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Close button functionality
        document.getElementById('closeButton').addEventListener('click', function() {
            // In a real implementation, this would close the modal
            console.log('Modal closed');
        });

        // Link buttons functionality
        document.querySelectorAll('.link-button').forEach(button => {
            button.addEventListener('click', function() {
                // In a real implementation, this would open the respective link
                console.log('Opening: ' + this.textContent);
            });
        });
    </script>
</body>
</html>