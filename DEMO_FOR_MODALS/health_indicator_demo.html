<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chatbot Model Health Status</title>
    <!-- Adobe Clean Font -->
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Adobe CEP Inspired Styles */
        :root {
            --cep-bg-dark: #2A2A2A;
            --cep-panel-dark: #363636;
            --cep-border-dark: #4D4D4D;
            --cep-text-primary: #F0F0F0;
            --cep-text-secondary: #B3B3B3;
            --cep-blue: #0078D7;
            --cep-green: #00B294;
            --cep-yellow: #FFB900;
            --cep-red: #D83B01;
            --cep-highlight: rgba(255, 255, 255, 0.05);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Source Sans Pro', sans-serif;
            background-color: var(--cep-bg-dark);
            color: var(--cep-text-primary);
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            padding: 20px;
            overflow-x: hidden;
            font-weight: 400;
            line-height: 1.5;
        }

        /* Main Container - Adobe Panel Style */
        .container {
            background-color: var(--cep-panel-dark);
            border-radius: 4px;
            padding: 0;
            width: 100%;
            max-width: 900px;
            margin-top: 20px;
            border: 1px solid var(--cep-border-dark);
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }

        /* Header - Adobe Style */
        .header {
            background-color: rgba(0, 0, 0, 0.2);
            padding: 20px 30px;
            border-bottom: 1px solid var(--cep-border-dark);
        }

        h1 {
            color: var(--cep-text-primary);
            font-size: 22px;
            font-weight: 600;
            margin: 0;
            letter-spacing: 0.5px;
        }

        /* Content Area */
        .content {
            padding: 25px 30px;
        }

        /* Model Selector - Adobe Style */
        .model-selector {
            margin-bottom: 30px;
            display: flex;
            align-items: center;
        }

        .model-selector label {
            font-size: 13px;
            margin-right: 12px;
            color: var(--cep-text-secondary);
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .model-selector select {
            background-color: var(--cep-panel-dark);
            color: var(--cep-text-primary);
            border: 1px solid var(--cep-border-dark);
            border-radius: 3px;
            padding: 8px 12px;
            font-size: 13px;
            width: 240px;
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='10' height='6' viewBox='0 0 10 6'%3E%3Cpath fill='%23B3B3B3' d='M5 6L0 1h10z'/%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 12px center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .model-selector select:hover {
            background-color: var(--cep-highlight);
            border-color: var(--cep-blue);
        }

        .model-selector select:focus {
            outline: none;
            border-color: var(--cep-blue);
            box-shadow: 0 0 0 1px var(--cep-blue);
        }

        /* Status Grid - Adobe Card Layout */
        .status-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin-bottom: 30px;
        }

        /* Status Card - Adobe Panel Style */
        .status-card {
            background-color: rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            text-align: center;
            border: 1px solid var(--cep-border-dark);
            transition: all 0.2s ease;
        }

        .status-card:hover {
            background-color: var(--cep-highlight);
        }

        .status-card h2 {
            font-size: 12px;
            color: var(--cep-text-secondary);
            margin-bottom: 15px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-value {
            font-size: 28px;
            font-weight: 600;
            color: var(--cep-blue);
            transition: color 0.3s ease;
            margin-bottom: 15px;
        }

        /* Status value colors */
        .status-value.good { color: var(--cep-green); }
        .status-value.warning { color: var(--cep-yellow); }
        .status-value.critical { color: var(--cep-red); }

        /* Progress Bar - Adobe Style */
        .progress-bar-container {
            width: 100%;
            height: 6px;
            background-color: var(--cep-border-dark);
            border-radius: 3px;
            overflow: hidden;
            margin-top: 5px;
        }

        .progress-bar-fill {
            height: 100%;
            width: 0%;
            background-color: var(--cep-blue);
            transition: width 0.5s ease-out, background-color 0.3s ease;
        }

        .progress-bar-fill.good { background-color: var(--cep-green); }
        .progress-bar-fill.warning { background-color: var(--cep-yellow); }
        .progress-bar-fill.critical { background-color: var(--cep-red); }

        /* Status indicator */
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            vertical-align: middle;
            background-color: #666;
        }

        .status-indicator.online { background-color: var(--cep-green); }
        .status-indicator.degraded { background-color: var(--cep-yellow); }
        .status-indicator.offline { background-color: var(--cep-red); }

        /* Log Section - Adobe Panel Style */
        .log-section {
            background-color: rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            padding: 0;
            border: 1px solid var(--cep-border-dark);
            overflow: hidden;
        }

        .log-section h2 {
            font-size: 12px;
            color: var(--cep-text-secondary);
            padding: 15px 20px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            background-color: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid var(--cep-border-dark);
            margin: 0;
        }

        .log-output {
            background-color: rgba(0, 0, 0, 0.2);
            color: var(--cep-text-secondary);
            font-family: 'Consolas', 'Monaco', monospace;
            padding: 15px 20px;
            height: 200px;
            overflow-y: auto;
            font-size: 12px;
            line-height: 1.6;
        }

        .log-output p {
            margin-bottom: 8px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .log-output p:first-child {
            color: var(--cep-text-primary);
            font-weight: 600;
        }

        /* Scrollbar styling */
        .log-output::-webkit-scrollbar {
            width: 6px;
        }

        .log-output::-webkit-scrollbar-track {
            background: transparent;
        }

        .log-output::-webkit-scrollbar-thumb {
            background: var(--cep-border-dark);
            border-radius: 3px;
        }

        .log-output::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .container {
                margin-top: 10px;
            }
            
            .content {
                padding: 20px;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .model-selector {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .model-selector label {
                margin-bottom: 8px;
                margin-right: 0;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI CHATBOT MODEL HEALTH DASHBOARD</h1>
        </div>
        
        <div class="content">
            <div class="model-selector">
                <label for="modelSelect">Active Model</label>
                <select id="modelSelect">
                    <option value="model_a">ChatBot Model A</option>
                    <option value="model_b">ChatBot Model B</option>
                    <option value="model_c">ChatBot Model C</option>
                </select>
            </div>

            <div class="status-grid">
                <div class="status-card">
                    <h2>CPU Usage</h2>
                    <div id="cpuUsage" class="status-value">--%</div>
                    <div class="progress-bar-container">
                        <div id="cpuProgressBar" class="progress-bar-fill"></div>
                    </div>
                </div>
                <div class="status-card">
                    <h2>Memory Usage</h2>
                    <div id="memoryUsage" class="status-value">--GB</div>
                    <div class="progress-bar-container">
                        <div id="memoryProgressBar" class="progress-bar-fill"></div>
                    </div>
                </div>
                <div class="status-card">
                    <h2>Service Status</h2>
                    <div id="modelStatus" class="status-value">
                        <span id="statusIndicator" class="status-indicator"></span>
                        <span id="statusText">--</span>
                    </div>
                </div>
                <div class="status-card">
                    <h2>Response Latency</h2>
                    <div id="latency" class="status-value">--ms</div>
                    <div class="progress-bar-container">
                        <div id="latencyProgressBar" class="progress-bar-fill"></div>
                    </div>
                </div>
            </div>

            <div class="log-section">
                <h2>System Log</h2>
                <div id="logOutput" class="log-output">
                    <p>System initialized. Select a model to begin monitoring...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Get DOM elements
        const modelSelect = document.getElementById('modelSelect');
        const cpuUsageElem = document.getElementById('cpuUsage');
        const memoryUsageElem = document.getElementById('memoryUsage');
        const latencyElem = document.getElementById('latency');
        const modelStatusElem = document.getElementById('modelStatus');
        const statusIndicatorElem = document.getElementById('statusIndicator');
        const statusTextElem = document.getElementById('statusText');
        const logOutputElem = document.getElementById('logOutput');

        // Progress bar elements
        const cpuProgressBar = document.getElementById('cpuProgressBar');
        const memoryProgressBar = document.getElementById('memoryProgressBar');
        const latencyProgressBar = document.getElementById('latencyProgressBar');

        let updateInterval;

        // Function to generate random health data
        function generateHealthData(modelId) {
            const cpu = (Math.random() * 80 + 10).toFixed(1);
            const memory = (Math.random() * 7 + 1).toFixed(2);
            const latency = (Math.random() * 150 + 50).toFixed(0);

            let status;
            let statusClass;
            const statusRoll = Math.random();
            if (statusRoll < 0.8) {
                status = "Online";
                statusClass = "online";
            } else if (statusRoll < 0.95) {
                status = "Degraded";
                statusClass = "degraded";
            } else {
                status = "Offline";
                statusClass = "offline";
            }

            return { cpu, memory, latency, status, statusClass };
        }

        // Function to update the UI with new data
        function updateUI(data) {
            // CPU Usage
            cpuUsageElem.textContent = `${data.cpu}%`;
            let cpuClass = (data.cpu > 70 ? 'critical' : (data.cpu > 50 ? 'warning' : 'good'));
            cpuUsageElem.className = 'status-value ' + cpuClass;
            cpuProgressBar.style.width = `${data.cpu}%`;
            cpuProgressBar.className = 'progress-bar-fill ' + cpuClass;

            // Memory Usage
            memoryUsageElem.textContent = `${data.memory}GB`;
            let memoryClass = (data.memory > 6 ? 'critical' : (data.memory > 4 ? 'warning' : 'good'));
            memoryUsageElem.className = 'status-value ' + memoryClass;
            memoryProgressBar.style.width = `${(data.memory / 8 * 100).toFixed(1)}%`;
            memoryProgressBar.className = 'progress-bar-fill ' + memoryClass;

            // Latency
            latencyElem.textContent = `${data.latency}ms`;
            let latencyClass = (data.latency > 150 ? 'critical' : (data.latency > 100 ? 'warning' : 'good'));
            latencyElem.className = 'status-value ' + latencyClass;
            latencyProgressBar.style.width = `${(data.latency / 200 * 100).toFixed(1)}%`;
            latencyProgressBar.className = 'progress-bar-fill ' + latencyClass;

            // Model Status
            statusTextElem.textContent = data.status;
            statusIndicatorElem.className = `status-indicator ${data.statusClass}`;
            modelStatusElem.className = 'status-value';
        }

        // Function to add a log entry
        function addLogEntry(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('p');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logOutputElem.prepend(logEntry);
            
            // Keep log output from getting too long
            if (logOutputElem.children.length > 50) {
                logOutputElem.removeChild(logOutputElem.lastChild);
            }
        }

        // Main function to start real-time updates
        function startRealtimeUpdates() {
            if (updateInterval) {
                clearInterval(updateInterval);
            }

            const selectedModel = modelSelect.value;
            addLogEntry(`Now monitoring: ${modelSelect.options[modelSelect.selectedIndex].text}`);

            // Initial update
            const initialData = generateHealthData(selectedModel);
            updateUI(initialData);
            addLogEntry(`Initial status: CPU ${initialData.cpu}% | Memory ${initialData.memory}GB | Latency ${initialData.latency}ms | Status: ${initialData.status}`);

            // Set up interval for continuous updates
            updateInterval = setInterval(() => {
                const data = generateHealthData(selectedModel);
                updateUI(data);
                
                // Only log significant changes
                if (Math.random() > 0.7) {
                    addLogEntry(`Update: CPU ${data.cpu}% | Memory ${data.memory}GB | Latency ${data.latency}ms`);
                }
            }, 3000);
        }

        // Event listeners
        modelSelect.addEventListener('change', startRealtimeUpdates);
        window.addEventListener('load', startRealtimeUpdates);
    </script>
</body>
</html>