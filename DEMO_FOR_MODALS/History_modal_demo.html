<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat History</title>
    <!-- Adobe Clean Font -->
    <link href="https://fonts.googleapis.com/css2?family=Source+Sans+Pro:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        /* Adobe CEP Inspired Styles */
        :root {
            --cep-bg-dark: #2A2A2A;
            --cep-panel-dark: #363636;
            --cep-border-dark: #4D4D4D;
            --cep-text-primary: #F0F0F0;
            --cep-text-secondary: #B3B3B3;
            --cep-blue: #0078D7;
            --cep-green: #00B294;
            --cep-yellow: #FFB900;
            --cep-red: #D83B01;
            --cep-highlight: rgba(255, 255, 255, 0.05);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Source Sans Pro', sans-serif;
            background-color: var(--cep-bg-dark);
            color: var(--cep-text-primary);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        /* Modal Container */
        .modal-container {
            width: 100%;
            max-width: 600px;
            height: 700px;
            background-color: var(--cep-panel-dark);
            border-radius: 4px;
            border: 1px solid var(--cep-border-dark);
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }

        /* Modal Header */
        .modal-header {
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid var(--cep-border-dark);
        }

        .modal-title {
            font-size: 16px;
            font-weight: 600;
        }

        .modal-close-button {
            background: none;
            border: none;
            color: var(--cep-text-secondary);
            font-size: 24px;
            cursor: pointer;
            padding: 0;
            line-height: 1;
        }

        .modal-close-button:hover {
            color: var(--cep-text-primary);
        }

        /* Modal Content */
        .modal-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
        }

        /* Search Section */
        .search-section {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .search-input-wrapper {
            flex: 1;
            position: relative;
        }

        .search-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--cep-text-secondary);
            pointer-events: none;
        }

        .search-input {
            width: 100%;
            padding: 8px 12px 8px 32px;
            background-color: var(--cep-panel-dark);
            border: 1px solid var(--cep-border-dark);
            border-radius: 3px;
            color: var(--cep-text-primary);
            font-size: 13px;
        }

        .search-input:focus {
            outline: none;
            border-color: var(--cep-blue);
        }

        .sort-select {
            padding: 8px 12px;
            background-color: var(--cep-panel-dark);
            border: 1px solid var(--cep-border-dark);
            border-radius: 3px;
            color: var(--cep-text-primary);
            font-size: 13px;
            cursor: pointer;
        }

        /* Session List */
        .session-list {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 15px;
        }

        .empty-state {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 300px;
            text-align: center;
            color: var(--cep-text-secondary);
        }

        .empty-icon {
            margin-bottom: 15px;
            color: var(--cep-text-secondary);
        }

        .empty-state h3 {
            font-size: 16px;
            margin-bottom: 5px;
            color: var(--cep-text-primary);
        }

        .empty-state p {
            font-size: 13px;
        }

        /* Session Item */
        .session-item {
            padding: 15px;
            margin-bottom: 10px;
            background-color: rgba(0, 0, 0, 0.15);
            border: 1px solid var(--cep-border-dark);
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .session-item:hover {
            background-color: var(--cep-highlight);
            border-color: var(--cep-blue);
        }

        .session-item.current-session {
            border-left: 3px solid var(--cep-blue);
            background-color: rgba(0, 120, 215, 0.1);
        }

        .session-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .session-title {
            font-size: 14px;
            font-weight: 600;
            margin: 0;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .session-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .session-date {
            font-size: 11px;
            color: var(--cep-text-secondary);
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .delete-button {
            background: none;
            border: none;
            color: var(--cep-text-secondary);
            cursor: pointer;
            padding: 0;
            display: flex;
            align-items: center;
        }

        .delete-button:hover {
            color: var(--cep-red);
        }

        .session-preview {
            font-size: 13px;
            color: var(--cep-text-secondary);
            margin-bottom: 10px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.4;
        }

        .session-meta {
            display: flex;
            justify-content: space-between;
            font-size: 11px;
            color: var(--cep-text-secondary);
        }

        .message-count {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .session-provider {
            background-color: rgba(0, 178, 148, 0.15);
            color: var(--cep-green);
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 10px;
        }

        /* History Summary */
        .history-summary {
            font-size: 12px;
            color: var(--cep-text-secondary);
            text-align: right;
            padding-top: 10px;
            border-top: 1px solid var(--cep-border-dark);
        }

        /* Scrollbar styling */
        .modal-content::-webkit-scrollbar,
        .session-list::-webkit-scrollbar {
            width: 6px;
        }

        .modal-content::-webkit-scrollbar-track,
        .session-list::-webkit-scrollbar-track {
            background: transparent;
        }

        .modal-content::-webkit-scrollbar-thumb,
        .session-list::-webkit-scrollbar-thumb {
            background: var(--cep-border-dark);
            border-radius: 3px;
        }

        .modal-content::-webkit-scrollbar-thumb:hover,
        .session-list::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
    </style>
</head>
<body>
    <div class="modal-container">
        <div class="modal-header">
            <span class="modal-title">Chat History</span>
            <button class="modal-close-button" id="closeButton">×</button>
        </div>
        
        <div class="modal-content">
            <!-- Search and Filter Controls -->
            <div class="search-section">
                <div class="search-input-wrapper">
                    <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="11" cy="11" r="8"></circle>
                        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                    </svg>
                    <input type="text" class="search-input" id="searchInput" placeholder="Search chat history...">
                </div>
                
                <select class="sort-select" id="sortSelect">
                    <option value="recent">Most Recent</option>
                    <option value="oldest">Oldest First</option>
                    <option value="alphabetical">Alphabetical</option>
                </select>
            </div>

            <!-- Session List -->
            <div class="session-list" id="sessionList">
                <!-- Example session items (would be dynamically generated in a real app) -->
                <div class="session-item current-session">
                    <div class="session-header">
                        <h4 class="session-title">Design System Discussion</h4>
                        <div class="session-actions">
                            <span class="session-date">
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12 6 12 12 16 14"></polyline>
                                </svg>
                                Just now
                            </span>
                            <button class="delete-button" title="Delete session">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="3 6 5 6 21 6"></polyline>
                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="session-preview">
                        I think we should use a modular design system approach that allows for easy component reuse across different parts of the application...
                    </div>
                    
                    <div class="session-meta">
                        <span class="message-count">12 messages</span>
                        <span class="session-provider">OpenAI</span>
                    </div>
                </div>

                <div class="session-item">
                    <div class="session-header">
                        <h4 class="session-title">API Integration Questions</h4>
                        <div class="session-actions">
                            <span class="session-date">
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12 6 12 12 16 14"></polyline>
                                </svg>
                                3 hours ago
                            </span>
                            <button class="delete-button" title="Delete session">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="3 6 5 6 21 6"></polyline>
                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="session-preview">
                        What's the best way to handle authentication for our new API? Should we use JWT tokens or session-based authentication...
                    </div>
                    
                    <div class="session-meta">
                        <span class="message-count">8 messages</span>
                        <span class="session-provider">Anthropic</span>
                    </div>
                </div>

                <div class="session-item">
                    <div class="session-header">
                        <h4 class="session-title">Performance Optimization</h4>
                        <div class="session-actions">
                            <span class="session-date">
                                <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <circle cx="12" cy="12" r="10"></circle>
                                    <polyline points="12 6 12 12 16 14"></polyline>
                                </svg>
                                Yesterday
                            </span>
                            <button class="delete-button" title="Delete session">
                                <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <polyline points="3 6 5 6 21 6"></polyline>
                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <div class="session-preview">
                        Our dashboard is loading too slowly. Let's discuss strategies for optimizing the performance...
                    </div>
                    
                    <div class="session-meta">
                        <span class="message-count">5 messages</span>
                        <span class="session-provider">Google Gemini</span>
                    </div>
                </div>
            </div>

            <!-- History Summary -->
            <div class="history-summary">
                <p>Showing 3 of 12 chat sessions</p>
            </div>
        </div>
    </div>

    <script>
        // Basic functionality for demo purposes
        document.getElementById('closeButton').addEventListener('click', function() {
            console.log('Modal closed');
        });

        // Search functionality
        document.getElementById('searchInput').addEventListener('input', function(e) {
            const query = e.target.value.toLowerCase();
            const sessions = document.querySelectorAll('.session-item');
            let visibleCount = 0;

            sessions.forEach(session => {
                const title = session.querySelector('.session-title').textContent.toLowerCase();
                const preview = session.querySelector('.session-preview').textContent.toLowerCase();
                const matches = title.includes(query) || preview.includes(query);
                
                session.style.display = matches ? 'block' : 'none';
                if (matches) visibleCount++;
            });

            // Update summary
            document.querySelector('.history-summary p').textContent = 
                `Showing ${visibleCount} of ${sessions.length} chat sessions`;
        });

        // Sort functionality
        document.getElementById('sortSelect').addEventListener('change', function(e) {
            const sortBy = e.target.value;
            const sessionList = document.getElementById('sessionList');
            const sessions = Array.from(document.querySelectorAll('.session-item'));

            sessions.sort((a, b) => {
                const aTitle = a.querySelector('.session-title').textContent;
                const bTitle = b.querySelector('.session-title').textContent;
                const aDate = a.querySelector('.session-date').textContent;
                const bDate = b.querySelector('.session-date').textContent;

                if (sortBy === 'alphabetical') {
                    return aTitle.localeCompare(bTitle);
                } else if (sortBy === 'oldest') {
                    // Simplified for demo - would use actual dates in real app
                    return aDate.localeCompare(bDate);
                } else {
                    // Default to recent first
                    return bDate.localeCompare(aDate);
                }
            });

            // Re-append sorted sessions
            sessions.forEach(session => sessionList.appendChild(session));
        });

        // Delete button functionality
        document.querySelectorAll('.delete-button').forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                if (confirm('Are you sure you want to delete this chat session?')) {
                    const sessionItem = this.closest('.session-item');
                    sessionItem.style.display = 'none';
                    console.log('Session deleted');
                }
            });
        });

        // Session click functionality
        document.querySelectorAll('.session-item').forEach(item => {
            item.addEventListener('click', function() {
                // Remove current-session class from all items
                document.querySelectorAll('.session-item').forEach(i => {
                    i.classList.remove('current-session');
                });
                // Add to clicked item
                this.classList.add('current-session');
                console.log('Session selected:', this.querySelector('.session-title').textContent);
            });
        });
    </script>
</body>
</html>