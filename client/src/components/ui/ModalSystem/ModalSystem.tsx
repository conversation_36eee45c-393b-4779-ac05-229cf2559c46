/**
 * Modal System for V2
 * Simple modal system that renders modals based on the modal store state
 */

import React from 'react';
import { useModalStore, ModalType } from '../../../stores/modalStore';
import { ChatHistoryModal, ProviderHealthModal, SettingsModal, AboutModal, HelpModal } from '../../modals';
import { SahAIModelConfiguration } from '../../SahAIModelConfiguration/SahAIModelConfiguration';
import './ModalSystem.css';

interface ModalConfig {
  id: ModalType;
  label: string;
  component: React.ComponentType<{ onClose: () => void; onBack: () => void }>;
}

const MODAL_CONFIGS: ModalConfig[] = [
  { id: 'settings', label: 'Settings', component: SettingsModal },
  { id: 'chatHistory', label: 'Chat History', component: ChatHistoryModal },
  { id: 'providerHealth', label: 'Provider Health', component: ProviderHealthModal },
  { id: 'about', label: 'About', component: AboutModal },
  { id: 'help', label: 'Help', component: HelpModal },
];

export const ModalSystem: React.FC = () => {
  const { currentModal, isOpen, closeModal } = useModalStore();

  const handleClose = () => {
    closeModal();
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isOpen || !currentModal) {
    return null;
  }

  // Handle model configuration modal separately since it has different props
  if (currentModal === 'modelConfiguration') {
    return (
      <div className="modal-backdrop" onClick={handleBackdropClick}>
        <div className="modal-container">
          <div className="modal-header">
            <h2>Model Configuration</h2>
            <button className="modal-close-button" onClick={handleClose}>
              ×
            </button>
          </div>
          <div className="modal-body">
            <SahAIModelConfiguration
              onClose={handleClose}
              isPopup={false}
            />
          </div>
        </div>
      </div>
    );
  }

  // Find the modal configuration
  const modalConfig = MODAL_CONFIGS.find(config => config.id === currentModal);
  
  if (!modalConfig) {
    console.warn(`Modal configuration not found for: ${currentModal}`);
    return null;
  }

  const ModalComponent = modalConfig.component;

  return (
    <div className="modal-backdrop" onClick={handleBackdropClick}>
      <div className="modal-container">
        <div className="modal-header">
          <h2>{modalConfig.label}</h2>
          <button className="modal-close-button" onClick={handleClose}>
            ×
          </button>
        </div>
        <div className="modal-body">
          <ModalComponent 
            onClose={handleClose}
            onBack={handleClose}
          />
        </div>
      </div>
    </div>
  );
};
