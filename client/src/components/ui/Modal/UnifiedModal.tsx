/**
 * Unified Modal Component for V2
 * Slide-in modal system for consistent V2 user experience
 * Clinical precision implementation following audit recommendations
 */

import React, { useCallback, useTransition } from 'react';
import { useModalStore, ModalType, ModalVariant } from '../../../stores/modalStore';
import { ChatHistoryModal, ProviderHealthModal, SettingsModal, AboutModal, HelpModal } from '../../modals';
import { SahAIModelConfiguration } from '../../SahAIModelConfiguration/SahAIModelConfiguration';
import './UnifiedModal.css';

interface ModalConfig {
  id: ModalType;
  label: string;
  component: React.ComponentType<{ onClose: () => void; onBack: () => void }>;
}

interface UnifiedModalProps {
  isOpen: boolean;
  onClose: () => void;
}

// Modal categorization for different access methods
const SETTINGS_ONLY_MODALS: ModalType[] = [
  'analytics',
  'advancedConfig',
  'modelComparison',
  'multiModel',
  'help',
  'about'
];

const INDEPENDENT_MODALS: ModalType[] = [
  'providerHealth',
  'chatHistory',
  'modelConfiguration'
];

const MODAL_CONFIGS: ModalConfig[] = [
  { id: 'providerHealth', label: 'Provider Health', component: ProviderHealthModal },
  { id: 'chatHistory', label: 'Chat History', component: ChatHistoryModal },
  { id: 'settings', label: 'Settings', component: SettingsModal },
  { id: 'about', label: 'About', component: AboutModal },
  { id: 'help', label: 'Help', component: HelpModal },
  // Settings-only modals (placeholders for now)
  { id: 'analytics', label: 'Analytics', component: SettingsModal },
  { id: 'advancedConfig', label: 'Advanced Config', component: SettingsModal },
  { id: 'modelComparison', label: 'Model Comparison', component: SettingsModal },
  { id: 'multiModel', label: 'Multi Model', component: SettingsModal },
];

/**
 * Unified Modal Component
 * Slide-in modal for V2 consistent user experience
 */
export const UnifiedModal: React.FC<UnifiedModalProps> = ({
  isOpen,
  onClose,
}) => {
  const { currentModal, modalData, accessMethod, variant } = useModalStore();
  const [isPending, startTransition] = useTransition();

  const handleOpenSubModal = useCallback((modalType: ModalType) => {
    if (modalType === 'settings') return;

    startTransition(() => {
      useModalStore.getState().openSubModal(modalType);
    });
  }, []);

  const handleBackToSettings = useCallback(() => {
    startTransition(() => {
      useModalStore.getState().goBackToSettings();
    });
  }, []);

  const handleClose = useCallback(() => {
    startTransition(() => {
      useModalStore.getState().closeModal();
    });
    onClose();
  }, [onClose]);

  const handleBackdropClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  }, [handleClose]);

  if (!isOpen || !currentModal) {
    return null;
  }

  // Find the modal configuration
  const currentConfig = MODAL_CONFIGS.find(config => config.id === currentModal);

  // Determine if this modal should show settings navigation
  const showSettingsNav = accessMethod === 'settings' && currentModal !== 'settings';
  const isIndependentModal = INDEPENDENT_MODALS.includes(currentModal);

  // Render slide-in modal (V2 standard)
  return (
    <div className={`slide-modal-overlay ${isOpen ? 'open' : ''}`}>
      {isOpen && (
        <div className={`slide-modal ${isOpen ? 'open' : ''}`}>
          <div className="slide-modal-header">
            <div className="slide-header-content">
              {showSettingsNav && (
                <button
                  className="slide-back-button"
                  onClick={handleBackToSettings}
                  aria-label="Back to settings"
                >
                  ← Back
                </button>
              )}
              <h2 className="slide-modal-title">
                {currentConfig?.label || 'Modal'}
              </h2>
            </div>
            <button
              className="slide-close-button"
              onClick={handleClose}
              aria-label="Close modal"
            >
              ×
            </button>
          </div>
          <div className="slide-modal-body">
            {/* Handle modelConfiguration separately */}
            {currentModal === 'modelConfiguration' && (
              <SahAIModelConfiguration
                onClose={handleClose}
                isPopup={false}
              />
            )}

            {/* Render other modal content */}
            {currentConfig && currentModal !== 'modelConfiguration' && (
              <currentConfig.component
                onClose={handleClose}
                onBack={handleBackToSettings}
              />
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default UnifiedModal;
