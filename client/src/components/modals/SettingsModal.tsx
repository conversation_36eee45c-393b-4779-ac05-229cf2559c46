/**
 * Settings Modal for V2
 * Enhanced settings interface with improved design and navigation
 * Inspired by Settings_modal_demo.html
 */

import React from 'react';
import { useModalStore } from '../../stores/modalStore';
import styles from './ModalContent.module.css';

interface SettingsModalProps {
  onClose: () => void;
  onBack: () => void;
}

interface SettingLink {
  id: string;
  title: string;
  description: string;
  icon: string;
  modalType: string;
}

const CORE_FEATURES: SettingLink[] = [
  {
    id: 'analytics',
    title: 'Analytics',
    description: 'View usage statistics and performance metrics',
    icon: '📊',
    modalType: 'analytics'
  },
  {
    id: 'advancedConfig',
    title: 'Advanced Config',
    description: 'Configure advanced settings and preferences',
    icon: '⚙️',
    modalType: 'advancedConfig'
  }
];

const MODEL_TOOLS: SettingLink[] = [
  {
    id: 'modelComparison',
    title: 'Model Comparison',
    description: 'Compare different AI models side by side',
    icon: '🔄',
    modalType: 'modelComparison'
  },
  {
    id: 'multiModel',
    title: 'Multi-Model Chat',
    description: 'Chat with multiple models simultaneously',
    icon: '🤖',
    modalType: 'multiModel'
  }
];

const HELP_SUPPORT: SettingLink[] = [
  {
    id: 'help',
    title: 'Help & Support',
    description: 'Get help and find answers to common questions',
    icon: '❓',
    modalType: 'help'
  },
  {
    id: 'about',
    title: 'About',
    description: 'Learn more about SahAI Extension',
    icon: 'ℹ️',
    modalType: 'about'
  }
];

export const SettingsModal: React.FC<SettingsModalProps> = ({
  onClose,
  onBack
}) => {
  const { openSubModal } = useModalStore();

  const handleSettingClick = (modalType: string) => {
    console.log(`Opening: ${modalType}`);
    // Open the corresponding sub-modal
    openSubModal(modalType as any);
  };

  const renderSettingsSection = (title: string, links: SettingLink[]) => (
    <div className={styles['settings-section']}>
      <h3 className={styles['settings-section-title']}>{title}</h3>
      {links.map((link) => (
        <button
          key={link.id}
          className={styles['settings-link']}
          onClick={() => handleSettingClick(link.modalType)}
        >
          <span className={styles['settings-icon']}>{link.icon}</span>
          <div className={styles['settings-link-text']}>
            <div className={styles['settings-link-title']}>{link.title}</div>
            <div className={styles['settings-link-description']}>{link.description}</div>
          </div>
        </button>
      ))}
    </div>
  );

  return (
    <div className={styles['modal-content']}>
      <div className={styles['modal-header']}>
        <h2 className={styles['modal-title']}>Settings</h2>
      </div>

      <div className={styles['modal-body']}>
        {/* Core Features Section */}
        {renderSettingsSection('Core Features', CORE_FEATURES)}

        {/* Model Tools Section */}
        {renderSettingsSection('Model Tools', MODEL_TOOLS)}

        {/* Help & Support Section */}
        {renderSettingsSection('Help & Support', HELP_SUPPORT)}
      </div>
    </div>
  );
};

export default SettingsModal;
