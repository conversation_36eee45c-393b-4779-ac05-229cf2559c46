/**
 * Provider Health Modal for V2
 * Adapted from V1 for V2's modal system
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { CheckIcon, XIcon, InfoIcon, RefreshIcon } from '../ui';
import './ModalContent.module.css';

interface ProviderHealthModalProps {
  onClose: () => void;
  onBack: () => void;
}

interface HealthStatus {
  status: 'healthy' | 'warning' | 'error' | 'unknown';
  message: string;
  lastChecked: Date;
  responseTime?: number;
}

export const ProviderHealthModal: React.FC<ProviderHealthModalProps> = ({
  onClose,
  onBack
}) => {
  const { providers, currentProvider } = useSettingsStore();
  const [healthStatuses, setHealthStatuses] = useState<Record<string, HealthStatus>>({});
  const [isChecking, setIsChecking] = useState(false);

  // Mock health check function - replace with actual implementation
  const checkProviderHealth = async (providerId: string): Promise<HealthStatus> => {
    const provider = providers.find(p => p.id === providerId);
    if (!provider) {
      return {
        status: 'unknown',
        message: 'Provider not found',
        lastChecked: new Date()
      };
    }

    if (!provider.isConfigured) {
      return {
        status: 'warning',
        message: 'Provider not configured - API key required',
        lastChecked: new Date()
      };
    }

    // Simulate API health check
    const startTime = Date.now();
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    const responseTime = Date.now() - startTime;

    // Simulate random health status
    const isHealthy = Math.random() > 0.2; // 80% chance of being healthy
    
    return {
      status: isHealthy ? 'healthy' : 'error',
      message: isHealthy 
        ? 'Provider is responding normally' 
        : 'Provider is experiencing issues',
      lastChecked: new Date(),
      responseTime
    };
  };

  const runHealthChecks = async () => {
    setIsChecking(true);
    const newStatuses: Record<string, HealthStatus> = {};

    for (const provider of providers) {
      try {
        newStatuses[provider.id] = await checkProviderHealth(provider.id);
      } catch (error) {
        newStatuses[provider.id] = {
          status: 'error',
          message: 'Health check failed',
          lastChecked: new Date()
        };
      }
    }

    setHealthStatuses(newStatuses);
    setIsChecking(false);
  };

  useEffect(() => {
    runHealthChecks();
  }, []);

  const getStatusIcon = (status: HealthStatus['status']) => {
    switch (status) {
      case 'healthy':
        return <CheckIcon size={16} className="status-icon healthy" />;
      case 'warning':
        return <InfoIcon size={16} className="status-icon warning" />;
      case 'error':
        return <XIcon size={16} className="status-icon error" />;
      default:
        return <InfoIcon size={16} className="status-icon unknown" />;
    }
  };

  const getStatusColor = (status: HealthStatus['status']) => {
    switch (status) {
      case 'healthy': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'error': return '#F44336';
      default: return '#6c757d';
    }
  };

  const formatResponseTime = (responseTime?: number) => {
    if (!responseTime) return 'N/A';
    return `${responseTime}ms`;
  };

  const formatLastChecked = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className="modal-content">
      <div className="health-dashboard">
        <div className="dashboard-header">
          <h2>Provider Health Dashboard</h2>
          <button
            className="refresh-button"
            onClick={runHealthChecks}
            disabled={isChecking}
            title="Refresh health status"
          >
            <RefreshIcon size={16} className={isChecking ? 'spinning' : ''} />
            {isChecking ? 'Checking...' : 'Refresh'}
          </button>
        </div>

        <div className="health-overview">
          <div className="overview-stats">
            <div className="stat-item">
              <span className="stat-label">Total Providers</span>
              <span className="stat-value">{providers.length}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Configured</span>
              <span className="stat-value">
                {providers.filter(p => p.isConfigured).length}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Current Provider</span>
              <span className="stat-value">{currentProvider?.name || 'None'}</span>
            </div>
          </div>
        </div>

        <div className="providers-health-list">
          {providers.map(provider => {
            const health = healthStatuses[provider.id];
            return (
              <div key={provider.id} className="provider-health-item">
                <div className="provider-info">
                  <div className="provider-header">
                    <h3 className="provider-name">{provider.name}</h3>
                    <div className="provider-status">
                      {health ? getStatusIcon(health.status) : <InfoIcon size={16} />}
                      <span 
                        className="status-text"
                        style={{ color: health ? getStatusColor(health.status) : '#6c757d' }}
                      >
                        {health?.status || 'Checking...'}
                      </span>
                    </div>
                  </div>
                  
                  <div className="provider-details">
                    <div className="detail-row">
                      <span className="detail-label">Status:</span>
                      <span className="detail-value">
                        {health?.message || 'Checking health...'}
                      </span>
                    </div>
                    
                    {health?.responseTime && (
                      <div className="detail-row">
                        <span className="detail-label">Response Time:</span>
                        <span className="detail-value">
                          {formatResponseTime(health.responseTime)}
                        </span>
                      </div>
                    )}
                    
                    <div className="detail-row">
                      <span className="detail-label">Configuration:</span>
                      <span className="detail-value">
                        {provider.isConfigured ? 'Configured' : 'Not configured'}
                      </span>
                    </div>
                    
                    {health && (
                      <div className="detail-row">
                        <span className="detail-label">Last Checked:</span>
                        <span className="detail-value">
                          {formatLastChecked(health.lastChecked)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
