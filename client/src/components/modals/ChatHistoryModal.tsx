/**
 * Chat History Modal for V2
 * Enhanced design with improved UI patterns while preserving functionality
 */

import React, { useState, useMemo } from 'react';
import { useChatStore, ChatSession } from '../../stores/chatStore';
import { useModalStore } from '../../stores/modalStore';
import {
  SearchIcon,
  TrashIcon,
  MessageSquareIcon,
  ClockIcon
} from '../ui';
import styles from './ModalContent.module.css';

interface ChatHistoryModalProps {
  onSelectSession?: (sessionId: string) => void;
  onClose: () => void;
  onBack: () => void;
}

export const ChatHistoryModal: React.FC<ChatHistoryModalProps> = ({
  onSelectSession,
  onClose,
  onBack,
}) => {
  const { sessions, currentSession, deleteSession, loadSession } = useChatStore();
  const { closeModal } = useModalStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<'recent' | 'oldest' | 'alphabetical'>('recent');

  // Filter and sort sessions
  const filteredAndSortedSessions = useMemo(() => {
    let filtered = sessions.filter(session => 
      session.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      session.messages.some(msg => 
        typeof msg.content === 'string' && 
        msg.content.toLowerCase().includes(searchQuery.toLowerCase())
      )
    );

    switch (sortBy) {
      case 'recent':
        return filtered.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());
      case 'oldest':
        return filtered.sort((a, b) => new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime());
      case 'alphabetical':
        return filtered.sort((a, b) => a.title.localeCompare(b.title));
      default:
        return filtered;
    }
  }, [sessions, searchQuery, sortBy]);

  const handleSelectSession = (sessionId: string) => {
    loadSession(sessionId);
    onSelectSession?.(sessionId);
    closeModal();
  };

  const handleDeleteSession = (sessionId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (confirm('Are you sure you want to delete this chat session?')) {
      deleteSession(sessionId);
    }
  };

  const formatDate = (timestamp: number) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const getSessionPreview = (session: ChatSession) => {
    const lastMessage = session.messages[session.messages.length - 1];
    if (!lastMessage) return 'No messages';
    
    const content = typeof lastMessage.content === 'string' 
      ? lastMessage.content 
      : 'Rich content message';
    
    return content.length > 100 ? content.substring(0, 100) + '...' : content;
  };

  return (
    <div className={styles['modal-content']}>
      <div className={styles['modal-header']}>
        <h2 className={styles['modal-title']}>Chat History</h2>
        <div className={styles['modal-actions']}>
          <span className={styles['session-count']}>
            {filteredAndSortedSessions.length} session{filteredAndSortedSessions.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      <div className={styles['modal-body']}>
        {/* Search and Filter Controls */}
        <div className={styles['search-section']}>
          <div className={styles['search-input-wrapper']}>
            <SearchIcon size={16} className={styles['search-icon']} />
            <input
              type="text"
              placeholder="Search chat history..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className={styles['search-input']}
            />
          </div>

          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className={styles['sort-select']}
          >
            <option value="recent">Most Recent</option>
            <option value="oldest">Oldest First</option>
            <option value="alphabetical">Alphabetical</option>
          </select>
        </div>

        {/* Sessions List */}
        <div className={styles['session-list']}>
          {filteredAndSortedSessions.length === 0 ? (
            <div className={styles['empty-state']}>
              <MessageSquareIcon size={48} className={styles['empty-icon']} />
              <h3 className={styles['empty-title']}>No chat sessions found</h3>
              <p className={styles['empty-description']}>
                {searchQuery
                  ? 'Try adjusting your search terms'
                  : 'Start a new conversation to see it here'
                }
              </p>
            </div>
          ) : (
            filteredAndSortedSessions.map((session) => (
              <div
                key={session.id}
                className={`${styles['session-item']} ${
                  currentSession?.id === session.id ? styles['current-session'] : ''
                }`}
                onClick={() => handleSelectSession(session.id)}
              >
                <div className={styles['session-header']}>
                  <h4 className={styles['session-title']}>{session.title}</h4>
                  <div className={styles['session-actions']}>
                    <span className={styles['session-date']}>
                      <ClockIcon size={12} />
                      {formatDate(session.updatedAt)}
                    </span>
                    <button
                      className={styles['delete-button']}
                      onClick={(e) => handleDeleteSession(session.id, e)}
                      title="Delete session"
                    >
                      <TrashIcon size={14} />
                    </button>
                  </div>
                </div>

                <div className={styles['session-preview']}>
                  {getSessionPreview(session)}
                </div>

                <div className={styles['session-meta']}>
                  <span className={styles['message-count']}>
                    {session.messages.length} messages
                  </span>
                  {session.metadata?.provider && (
                    <span className={styles['session-provider']}>
                      {session.metadata.provider}
                    </span>
                  )}
                </div>
              </div>
            ))
          )}
        </div>

        {/* Summary */}
        {sessions.length > 0 && (
          <div className={styles['history-summary']}>
            <p className={styles['summary-text']}>
              Showing {filteredAndSortedSessions.length} of {sessions.length} chat sessions
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
