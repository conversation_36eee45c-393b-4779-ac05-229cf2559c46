/**
 * Standardized Modal Content Styles for V2
 * Adobe CEP-compatible design with consistent spacing and typography
 * Unified modal system for all modal types
 * Modal appears below topbar, covering message composer and input area
 */

/* ===== MODAL SYSTEM BASE ===== */

/* Base modal content container - standardized across all modals */
.modal-content {
  /* Layout */
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  min-height: 400px;
  max-height: calc(100vh - 120px); /* Account for topbar space */
  
  /* Spacing - standardized 8px grid system */
  padding: 24px;
  gap: 16px;
  
  /* Typography */
  color: var(--adobe-text-primary);
  font-family: var(--adobe-font-family);
  font-size: 14px;
  line-height: 1.5;
  
  /* Scrolling */
  overflow-y: auto;
  
  /* Responsive */
  min-width: 320px;
  box-sizing: border-box;
}

/* Modal header - standardized across all modals */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--adobe-border);
  margin-bottom: 8px;
  gap: 16px;
  flex-shrink: 0;
}

.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin: 0;
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.modal-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* Modal body - standardized scrollable content area */
.modal-body {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Modal footer - standardized across all modals */
.modal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid var(--adobe-border);
  margin-top: 8px;
  gap: 16px;
  flex-shrink: 0;
}

/* ===== STANDARDIZED FORM ELEMENTS ===== */

.modal-content input,
.modal-content textarea,
.modal-content select {
  width: 100%;
  height: 40px;
  padding: 8px 12px;
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 6px;
  color: var(--adobe-text-primary, #ffffff);
  font-size: 14px;
  font-family: var(--adobe-font-family);
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
  box-sizing: border-box;
}

.modal-content textarea {
  height: auto;
  min-height: 80px;
  resize: vertical;
}

.modal-content input:focus,
.modal-content textarea:focus,
.modal-content select:focus {
  border-color: var(--adobe-color-primary, #0a84ff);
  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.2);
}

.modal-content input:disabled,
.modal-content textarea:disabled,
.modal-content select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--adobe-bg-secondary, #2d2d2d);
}

/* ===== STANDARDIZED BUTTONS ===== */

.modal-content button {
  height: 40px;
  padding: 8px 16px;
  background: var(--adobe-color-primary, #0a84ff);
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  font-family: var(--adobe-font-family);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
  box-sizing: border-box;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.modal-content button:hover:not(:disabled) {
  background: var(--adobe-color-primary-hover, #0066cc);
  transform: translateY(-1px);
}

.modal-content button:focus {
  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.4);
}

.modal-content button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Button variants */
.modal-content button.secondary {
  background: var(--adobe-bg-tertiary, #3c3c3c);
  color: var(--adobe-text-primary, #ffffff);
  border: 1px solid var(--adobe-border, #555555);
}

.modal-content button.secondary:hover:not(:disabled) {
  background: var(--adobe-bg-hover, #4a4a4a);
  border-color: var(--adobe-color-primary, #0a84ff);
}

.modal-content button.danger {
  background: var(--adobe-bg-danger, #ff4444);
}

.modal-content button.danger:hover:not(:disabled) {
  background: #cc3333;
}

.modal-content button.icon-only {
  width: 40px;
  height: 40px;
  padding: 0;
  background: var(--adobe-bg-tertiary, #3c3c3c);
  color: var(--adobe-text-secondary, #cccccc);
  border: 1px solid var(--adobe-border, #555555);
}

.modal-content button.icon-only:hover:not(:disabled) {
  background: var(--adobe-bg-hover, #4a4a4a);
  border-color: var(--adobe-color-primary, #0a84ff);
  color: var(--adobe-text-primary, #ffffff);
}

/* ===== STANDARDIZED LAYOUT COMPONENTS ===== */

/* Toolbar - standardized across all modals */
.modal-toolbar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  border-bottom: 1px solid var(--adobe-border, #555555);
  margin-bottom: 16px;
  flex-shrink: 0;
}

.modal-toolbar-primary {
  flex: 1;
  min-width: 0;
}

.modal-toolbar-secondary {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

/* Card - standardized container for content blocks */
.modal-card {
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.modal-card:hover {
  background: var(--adobe-bg-hover, #4a4a4a);
  border-color: var(--adobe-color-primary, #0a84ff);
}

.modal-card.active {
  border-color: var(--adobe-color-primary, #0a84ff);
  background: rgba(10, 132, 255, 0.1);
}

.modal-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
  gap: 12px;
}

.modal-card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary, #ffffff);
  margin: 0;
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.modal-card-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.modal-card-body {
  color: var(--adobe-text-secondary, #cccccc);
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 12px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.modal-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--adobe-text-tertiary, #999999);
  gap: 8px;
}

/* List - standardized scrollable list container */
.modal-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  flex: 1;
  min-height: 0;
  overflow-y: auto;
}

.modal-list-item {
  cursor: pointer;
}

/* Grid - standardized grid layout */
.modal-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

/* Stats - standardized statistics display */
.modal-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  padding: 16px;
  background: var(--adobe-bg-secondary, #2d2d2d);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 8px;
}

.modal-stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
}

.modal-stat-label {
  font-size: 12px;
  color: var(--adobe-text-secondary, #cccccc);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 500;
}

.modal-stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--adobe-text-primary, #ffffff);
}

/* Empty state - standardized across all modals */
.modal-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  color: var(--adobe-text-secondary, #cccccc);
  flex: 1;
}

.modal-empty-icon {
  opacity: 0.5;
  margin-bottom: 16px;
  font-size: 48px;
}

.modal-empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--adobe-text-primary, #ffffff);
  margin: 0 0 8px 0;
}

.modal-empty-description {
  font-size: 14px;
  margin: 0;
  max-width: 400px;
}

/* ===== STANDARDIZED SCROLLBARS ===== */

.modal-content::-webkit-scrollbar,
.modal-body::-webkit-scrollbar,
.modal-list::-webkit-scrollbar {
  width: 8px;
}

.modal-content::-webkit-scrollbar-track,
.modal-body::-webkit-scrollbar-track,
.modal-list::-webkit-scrollbar-track {
  background: var(--adobe-bg-primary, #1e1e1e);
}

.modal-content::-webkit-scrollbar-thumb,
.modal-body::-webkit-scrollbar-thumb,
.modal-list::-webkit-scrollbar-thumb {
  background: var(--adobe-border, #555555);
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover,
.modal-body::-webkit-scrollbar-thumb:hover,
.modal-list::-webkit-scrollbar-thumb:hover {
  background: var(--adobe-text-secondary, #cccccc);
}

/* ===== RESPONSIVE DESIGN ===== */

/* Medium screens */
@media (max-width: 768px) {
  .modal-content {
    padding: 16px;
    gap: 12px;
  }
  
  .modal-toolbar,
  .searchSection {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .modal-toolbar-primary,
  .modal-toolbar-secondary,
  .searchInput,
  .searchButton,
  .sortSelect {
    width: 100%;
  }

  .modal-toolbar-secondary {
    justify-content: center;
  }

  .searchButton {
    height: 40px;
  }

  .sortSelect {
    min-width: unset;
    max-width: unset;
  }
  
  .modal-grid {
    grid-template-columns: 1fr;
  }
  
  .modal-stats {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  }
}

/* Small screens */
@media (max-width: 480px) {
  .modal-content {
    padding: 12px;
    gap: 8px;
  }
  
  .modal-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .modal-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .modal-card-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .modal-card-footer {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .modal-stats {
    grid-template-columns: 1fr;
  }
}

/* ===== ANIMATIONS ===== */

.modal-fade-in {
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ===== ABOUT MODAL STYLES ===== */

/* About Sections */
.about-section {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

/* Logo Section */
.logo-section {
  text-align: center;
  padding: 15px 0;
}

.app-icon {
  font-size: 48px;
  margin-bottom: 10px;
  display: block;
}

.app-name {
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 5px;
  color: var(--adobe-text-primary);
}

.version {
  font-size: 14px;
  color: var(--adobe-text-secondary);
}

/* Info Section */
.info-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.section-header {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 5px;
}

.section-description {
  font-size: 14px;
  color: var(--adobe-text-secondary);
  line-height: 1.5;
}

/* Features List */
.features-list {
  list-style: none;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 10px;
  padding-left: 0;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.feature-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.feature-item div {
  font-size: 14px;
  color: var(--adobe-text-primary);
}

.feature-item strong {
  color: var(--adobe-accent-primary);
}

/* Links Section */
.links-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.link-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.link-button {
  padding: 8px 12px;
  background-color: rgba(0, 120, 215, 0.2);
  border: 1px solid var(--adobe-accent-primary);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  font-family: var(--adobe-font-family);
}

.link-button:hover {
  background-color: rgba(0, 120, 215, 0.3);
  border-color: var(--adobe-accent-hover);
}

/* Copyright Section */
.copyright-section {
  margin-top: 20px;
  text-align: center;
  padding-top: 15px;
  border-top: 1px solid var(--adobe-border);
}

.copyright {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  margin-bottom: 5px;
}

.disclaimer {
  font-size: 11px;
  color: var(--adobe-text-secondary);
  font-style: italic;
}

/* ===== HELP MODAL STYLES ===== */

/* Help Sections */
.help-section {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.subsection-header {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 15px;
  padding-bottom: 5px;
  border-bottom: 1px solid var(--adobe-border);
}

/* Quick Start Guide */
.quick-start {
  margin-bottom: 20px;
}

.steps-list {
  padding-left: 20px;
  margin-bottom: 20px;
}

.steps-list li {
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--adobe-text-primary);
}

.steps-list strong {
  color: var(--adobe-accent-primary);
}

/* FAQ Section */
.faq-section {
  margin-bottom: 20px;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-bottom: 20px;
}

.faq-item {
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  overflow: hidden;
}

.faq-question {
  width: 100%;
  padding: 12px 15px;
  background-color: rgba(0, 0, 0, 0.15);
  border: none;
  color: var(--adobe-text-primary);
  text-align: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  transition: background-color 0.2s ease;
  font-family: var(--adobe-font-family);
}

.faq-question:hover {
  background-color: var(--adobe-bg-hover);
}

.faq-toggle {
  font-size: 18px;
  font-weight: bold;
  margin-left: 10px;
  color: var(--adobe-accent-primary);
}

.faq-answer {
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.1);
  font-size: 13px;
  color: var(--adobe-text-secondary);
  line-height: 1.5;
  border-top: 1px solid var(--adobe-border);
}

/* Support Section */
.support-section {
  margin-bottom: 20px;
}

.support-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  margin-bottom: 20px;
}

.support-button {
  padding: 10px 12px;
  background-color: rgba(0, 120, 215, 0.2);
  border: 1px solid var(--adobe-accent-primary);
  border-radius: 3px;
  color: var(--adobe-text-primary);
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: var(--adobe-font-family);
}

.support-button:hover {
  background-color: rgba(0, 120, 215, 0.3);
  border-color: var(--adobe-accent-hover);
}

/* Tips Section */
.tips-section {
  margin-bottom: 20px;
}

.tips-list {
  list-style: none;
  padding-left: 0;
}

.tip-item {
  margin-bottom: 10px;
  font-size: 14px;
  line-height: 1.5;
  padding-left: 20px;
  position: relative;
  color: var(--adobe-text-primary);
}

.tip-item::before {
  content: "•";
  color: var(--adobe-accent-primary);
  font-weight: bold;
  position: absolute;
  left: 5px;
}

.tip-item strong {
  color: var(--adobe-accent-primary);
}

/* ===== SETTINGS MODAL STYLES ===== */

/* Settings Sections */
.settings-section {
  margin-bottom: 30px;
}

.settings-section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--adobe-border);
}

/* Settings Links */
.settings-link {
  width: 100%;
  padding: 12px 15px;
  background-color: transparent;
  border: 1px solid var(--adobe-border);
  border-radius: 4px;
  color: var(--adobe-text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  transition: all 0.2s ease;
  text-align: left;
  font-family: var(--adobe-font-family);
}

.settings-link:hover {
  background-color: var(--adobe-bg-hover);
  border-color: var(--adobe-accent-primary);
}

.settings-icon {
  font-size: 20px;
  flex-shrink: 0;
  width: 24px;
  text-align: center;
}

.settings-link-text {
  flex: 1;
  min-width: 0;
}

.settings-link-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--adobe-text-primary);
  margin-bottom: 2px;
}

.settings-link-description {
  font-size: 12px;
  color: var(--adobe-text-secondary);
  line-height: 1.4;
}

/* ===== MODAL-SPECIFIC IMPLEMENTATIONS ===== */

/* Chat History Modal */
.chatHistorySection {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.searchSection {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  border-bottom: 1px solid var(--adobe-border, #555555);
  margin-bottom: 16px;
  flex-shrink: 0;
}

.searchInput {
  flex: 1;
  min-width: 0;
  height: 40px;
  padding: 8px 12px;
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 6px;
  color: var(--adobe-text-primary, #ffffff);
  font-size: 14px;
  box-sizing: border-box;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.searchInput:focus {
  border-color: var(--adobe-color-primary, #0a84ff);
  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.2);
}

.searchButton {
  width: 40px;
  height: 40px;
  padding: 0;
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 6px;
  color: var(--adobe-text-secondary, #cccccc);
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  box-sizing: border-box;
  outline: none;
}

.searchButton:hover {
  background: var(--adobe-bg-hover, #4a4a4a);
  border-color: var(--adobe-color-primary, #0a84ff);
  color: var(--adobe-text-primary, #ffffff);
}

.searchButton:focus {
  border-color: var(--adobe-color-primary, #0a84ff);
  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.2);
}

.sortSelect {
  min-width: 120px;
  max-width: 160px;
  height: 40px;
  padding: 8px 12px;
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 6px;
  color: var(--adobe-text-primary, #ffffff);
  font-size: 14px;
  cursor: pointer;
  flex-shrink: 0;
  box-sizing: border-box;
  outline: none;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.sortSelect:focus {
  border-color: var(--adobe-color-primary, #0a84ff);
  box-shadow: 0 0 0 2px rgba(10, 132, 255, 0.2);
}

.sessionList {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.sessionItem {
  background: var(--adobe-bg-tertiary, #3c3c3c);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.sessionItem:hover {
  background: var(--adobe-bg-hover, #4a4a4a);
  border-color: var(--adobe-color-primary, #0a84ff);
}

.currentSession {
  border-color: var(--adobe-color-primary, #0a84ff);
  background: rgba(10, 132, 255, 0.1);
}

.sessionHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 12px;
}

.sessionTitle {
  font-size: 16px;
  font-weight: 600;
  color: var(--adobe-text-primary, #ffffff);
  margin: 0;
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.sessionActions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.sessionDate {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--adobe-text-secondary, #cccccc);
  white-space: nowrap;
}

.deleteButton {
  background: none;
  border: none;
  color: var(--adobe-text-secondary, #cccccc);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  min-width: 24px;
  min-height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  outline: none;
}

.deleteButton:hover {
  background: var(--adobe-bg-danger, #ff4444);
  color: white;
}

.sessionPreview {
  font-size: 14px;
  color: var(--adobe-text-secondary, #cccccc);
  line-height: 1.4;
  margin-bottom: 8px;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.sessionMeta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: var(--adobe-text-tertiary, #999999);
  gap: 8px;
}

.messageCount {
  font-weight: 500;
  white-space: nowrap;
}

.sessionProvider {
  background: var(--adobe-bg-secondary, #2d2d2d);
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  white-space: nowrap;
  flex-shrink: 0;
}

.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: var(--adobe-text-secondary, #cccccc);
}

.emptyIcon {
  opacity: 0.5;
  margin-bottom: 16px;
}

.emptyState h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: var(--adobe-text-primary, #ffffff);
}

.emptyState p {
  margin: 0;
  font-size: 14px;
}

.historySummary {
  padding: 12px 0;
  border-top: 1px solid var(--adobe-border, #555555);
  text-align: center;
  font-size: 12px;
  color: var(--adobe-text-secondary, #cccccc);
  flex-shrink: 0;
}

.historySummary p {
  margin: 0;
}

/* Provider Health Modal */
.health-dashboard {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.dashboard-header {
  /* Uses modal-header classes */
}

.health-overview {
  /* Uses modal-stats classes */
}

.providers-health-list {
  /* Uses modal-list classes */
}

.provider-health-item {
  /* Uses modal-card classes */
}

/* Settings Modal */
.settings-modal {
  display: flex;
  height: 100%;
  gap: 20px;
}

.settings-sidebar {
  width: 280px;
  background: var(--adobe-bg-secondary, #2d2d2d);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 8px;
  padding: 16px;
  flex-shrink: 0;
}

.settings-nav {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.settings-nav-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: var(--adobe-text-primary, #ffffff);
  cursor: pointer;
  text-align: left;
  transition: background 0.2s ease;
  width: 100%;
  box-sizing: border-box;
  font-size: 14px;
  font-family: var(--adobe-font-family);
  height: auto;
}

.settings-nav-item:hover {
  background: var(--adobe-bg-tertiary, #3c3c3c);
}

.settings-nav-item.active {
  background: rgba(10, 132, 255, 0.1);
  color: var(--adobe-color-primary, #0a84ff);
}

.nav-item-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.nav-item-title {
  font-size: 14px;
  font-weight: 500;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.nav-item-description {
  font-size: 12px;
  color: var(--adobe-text-secondary, #cccccc);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.settings-content {
  flex: 1;
  background: var(--adobe-bg-secondary, #2d2d2d);
  border: 1px solid var(--adobe-border, #555555);
  border-radius: 8px;
  padding: 24px;
  overflow-y: auto;
  min-width: 0;
}

/* Settings responsive */
@media (max-width: 768px) {
  .settings-modal {
    flex-direction: column;
    gap: 16px;
  }
  
  .settings-sidebar {
    width: 100%;
    order: 2;
  }
  
  .settings-content {
    order: 1;
  }
}

/* ===== UTILITY CLASSES ===== */

.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.flex-1 { flex: 1; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-grow-0 { flex-grow: 0; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.mb-0 { margin-bottom: 0; }
.mb-8 { margin-bottom: 8px; }
.mb-16 { margin-bottom: 16px; }
.mb-24 { margin-bottom: 24px; }

.gap-4 { gap: 4px; }
.gap-8 { gap: 8px; }
.gap-12 { gap: 12px; }
.gap-16 { gap: 16px; }
.gap-24 { gap: 24px; }

.p-8 { padding: 8px; }
.p-12 { padding: 12px; }
.p-16 { padding: 16px; }
.p-24 { padding: 24px; }

.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }

.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

.whitespace-nowrap { white-space: nowrap; }
.break-words { word-wrap: break-word; overflow-wrap: break-word; }