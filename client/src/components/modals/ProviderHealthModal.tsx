/**
 * Provider Health Modal for V2
 * Enhanced design with improved health monitoring UI
 */

import React, { useState, useEffect } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { CheckIcon, XIcon, InfoIcon, RefreshIcon } from '../ui';
import styles from './ModalContent.module.css';

interface ProviderHealthModalProps {
  onClose: () => void;
  onBack: () => void;
}

interface HealthStatus {
  status: 'healthy' | 'warning' | 'error' | 'unknown';
  message: string;
  lastChecked: Date;
  responseTime?: number;
}

export const ProviderHealthModal: React.FC<ProviderHealthModalProps> = ({
  onClose,
  onBack
}) => {
  const { providers, currentProvider } = useSettingsStore();
  const [healthStatuses, setHealthStatuses] = useState<Record<string, HealthStatus>>({});
  const [isChecking, setIsChecking] = useState(false);

  // Mock health check function - replace with actual implementation
  const checkProviderHealth = async (providerId: string): Promise<HealthStatus> => {
    const provider = providers.find(p => p.id === providerId);
    if (!provider) {
      return {
        status: 'unknown',
        message: 'Provider not found',
        lastChecked: new Date()
      };
    }

    if (!provider.isConfigured) {
      return {
        status: 'warning',
        message: 'Provider not configured - API key required',
        lastChecked: new Date()
      };
    }

    // Simulate API health check
    const startTime = Date.now();
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    const responseTime = Date.now() - startTime;

    // Simulate random health status
    const isHealthy = Math.random() > 0.2; // 80% chance of being healthy
    
    return {
      status: isHealthy ? 'healthy' : 'error',
      message: isHealthy 
        ? 'Provider is responding normally' 
        : 'Provider is experiencing issues',
      lastChecked: new Date(),
      responseTime
    };
  };

  const runHealthChecks = async () => {
    setIsChecking(true);
    const newStatuses: Record<string, HealthStatus> = {};

    for (const provider of providers) {
      try {
        newStatuses[provider.id] = await checkProviderHealth(provider.id);
      } catch (error) {
        newStatuses[provider.id] = {
          status: 'error',
          message: 'Health check failed',
          lastChecked: new Date()
        };
      }
    }

    setHealthStatuses(newStatuses);
    setIsChecking(false);
  };

  useEffect(() => {
    runHealthChecks();
  }, []);

  const getStatusIcon = (status: HealthStatus['status']) => {
    switch (status) {
      case 'healthy':
        return <CheckIcon size={16} className="status-icon healthy" />;
      case 'warning':
        return <InfoIcon size={16} className="status-icon warning" />;
      case 'error':
        return <XIcon size={16} className="status-icon error" />;
      default:
        return <InfoIcon size={16} className="status-icon unknown" />;
    }
  };

  const getStatusColor = (status: HealthStatus['status']) => {
    switch (status) {
      case 'healthy': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'error': return '#F44336';
      default: return '#6c757d';
    }
  };

  const formatResponseTime = (responseTime?: number) => {
    if (!responseTime) return 'N/A';
    return `${responseTime}ms`;
  };

  const formatLastChecked = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    
    const diffHours = Math.floor(diffMins / 60);
    if (diffHours < 24) return `${diffHours}h ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className={styles['modal-content']}>
      <div className={styles['modal-header']}>
        <h2 className={styles['modal-title']}>Provider Health Dashboard</h2>
        <div className={styles['modal-actions']}>
          <button
            className={styles['refresh-button']}
            onClick={runHealthChecks}
            disabled={isChecking}
            title="Refresh health status"
          >
            <RefreshIcon size={16} className={isChecking ? styles['spinning'] : ''} />
            {isChecking ? 'Checking...' : 'Refresh'}
          </button>
        </div>
      </div>

      <div className={styles['modal-body']}>
        <div className={styles['health-overview']}>
          <div className={styles['overview-stats']}>
            <div className={styles['stat-item']}>
              <span className={styles['stat-label']}>Total Providers</span>
              <span className={styles['stat-value']}>{providers.length}</span>
            </div>
            <div className={styles['stat-item']}>
              <span className={styles['stat-label']}>Configured</span>
              <span className={styles['stat-value']}>
                {providers.filter(p => p.isConfigured).length}
              </span>
            </div>
            <div className={styles['stat-item']}>
              <span className={styles['stat-label']}>Current Provider</span>
              <span className={styles['stat-value']}>{currentProvider?.name || 'None'}</span>
            </div>
          </div>
        </div>

        <div className={styles['providers-health-list']}>
          {providers.map(provider => {
            const health = healthStatuses[provider.id];
            return (
              <div key={provider.id} className={styles['provider-health-item']}>
                <div className={styles['provider-info']}>
                  <div className={styles['provider-header']}>
                    <h3 className={styles['provider-name']}>{provider.name}</h3>
                    <div className={styles['provider-status']}>
                      {health ? getStatusIcon(health.status) : <InfoIcon size={16} />}
                      <span
                        className={styles['status-text']}
                        style={{ color: health ? getStatusColor(health.status) : '#6c757d' }}
                      >
                        {health?.status || 'Checking...'}
                      </span>
                    </div>
                  </div>

                  <div className={styles['provider-details']}>
                    <div className={styles['detail-row']}>
                      <span className={styles['detail-label']}>Status:</span>
                      <span className={styles['detail-value']}>
                        {health?.message || 'Checking health...'}
                      </span>
                    </div>

                    {health?.responseTime && (
                      <div className={styles['detail-row']}>
                        <span className={styles['detail-label']}>Response Time:</span>
                        <span className={styles['detail-value']}>
                          {formatResponseTime(health.responseTime)}
                        </span>
                      </div>
                    )}

                    <div className={styles['detail-row']}>
                      <span className={styles['detail-label']}>Configuration:</span>
                      <span className={styles['detail-value']}>
                        {provider.isConfigured ? 'Configured' : 'Not configured'}
                      </span>
                    </div>

                    {health && (
                      <div className={styles['detail-row']}>
                        <span className={styles['detail-label']}>Last Checked:</span>
                        <span className={styles['detail-value']}>
                          {formatLastChecked(health.lastChecked)}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ProviderHealthModal;
