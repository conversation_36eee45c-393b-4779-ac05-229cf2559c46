/**
 * Help Modal for V2
 * Provides comprehensive help and support information
 * Inspired by Help_modal_demo.html
 */

import React, { useState } from 'react';
import styles from './ModalContent.module.css';

interface HelpModalProps {
  onClose: () => void;
  onBack: () => void;
}

interface FAQItem {
  id: string;
  question: string;
  answer: string;
}

const FAQ_ITEMS: FAQItem[] = [
  {
    id: 'api-keys',
    question: 'How do I set up API keys for AI providers?',
    answer: 'Go to Settings > Advanced Config to configure API keys for different AI providers. Your keys are stored securely and encrypted locally.'
  },
  {
    id: 'adobe-support',
    question: 'Which Adobe applications are supported?',
    answer: 'SahAI supports After Effects 2025, Premiere Pro 2025, Photoshop 2025, and Illustrator 2025. Make sure you have the latest versions installed.'
  },
  {
    id: 'model-comparison',
    question: 'How do I compare different AI models?',
    answer: 'Use the Model Comparison feature from the settings menu to test different models side by side and see their performance metrics.'
  },
  {
    id: 'multi-model',
    question: 'Can I use multiple AI models simultaneously?',
    answer: 'Yes! The Multi-Model Chat feature allows you to chat with multiple AI models in one conversation thread for better results.'
  },
  {
    id: 'troubleshooting',
    question: 'How do I troubleshoot connection issues?',
    answer: 'Check your internet connection, verify your API keys are correct, and ensure the AI provider services are operational. Check the Analytics panel for detailed error logs.'
  },
  {
    id: 'data-security',
    question: 'Is my data secure?',
    answer: 'Yes, all API keys are encrypted and stored locally. Your conversations are not stored permanently and are only sent to the AI providers you\'ve configured.'
  }
];

export const HelpModal: React.FC<HelpModalProps> = ({ onClose, onBack }) => {
  const [expandedFAQ, setExpandedFAQ] = useState<string | null>(null);

  const toggleFAQ = (faqId: string) => {
    setExpandedFAQ(expandedFAQ === faqId ? null : faqId);
  };

  const handleSupportClick = (supportType: string) => {
    console.log(`Opening: ${supportType}`);
    // In a real implementation, these would open actual links
    switch (supportType) {
      case 'Full Documentation':
        // window.open('https://github.com/sahai-ai/cep-extension/wiki', '_blank');
        break;
      case 'Community Discussions':
        // window.open('https://github.com/sahai-ai/cep-extension/discussions', '_blank');
        break;
      case 'Report a Bug':
        // window.open('https://github.com/sahai-ai/cep-extension/issues/new', '_blank');
        break;
      case 'Contact Support':
        // window.open('mailto:<EMAIL>', '_blank');
        break;
      default:
        break;
    }
  };

  return (
    <div className={styles['modal-content']}>
      <div className={styles['modal-header']}>
        <h2 className={styles['modal-title']}>Help & Support</h2>
      </div>
      
      <div className={styles['modal-body']}>
        <div className={styles['help-section']}>
          <h3 className={styles['section-header']}>Help & Support</h3>
          <p className={styles['section-description']}>
            Find quick answers and guides to get the most out of SahAI.
          </p>

          {/* Quick Start Guide */}
          <div className={styles['quick-start']}>
            <h4 className={styles['subsection-header']}>Quick Start Guide</h4>
            <ol className={styles['steps-list']}>
              <li><strong>Configure Provider:</strong> Go to Settings and enter your API key.</li>
              <li><strong>Select Model:</strong> Choose your preferred AI model from the dropdown.</li>
              <li><strong>Start Chatting:</strong> Type your query and get instant AI responses.</li>
              <li><strong>Explore Features:</strong> Check out analytics and multi-model chat.</li>
            </ol>
          </div>

          {/* FAQ Section */}
          <div className={styles['faq-section']}>
            <h4 className={styles['subsection-header']}>Frequently Asked Questions</h4>
            <div className={styles['faq-list']}>
              {FAQ_ITEMS.map((faq) => (
                <div key={faq.id} className={styles['faq-item']}>
                  <button 
                    className={styles['faq-question']}
                    onClick={() => toggleFAQ(faq.id)}
                  >
                    <span>{faq.question}</span>
                    <span className={styles['faq-toggle']}>
                      {expandedFAQ === faq.id ? '−' : '+'}
                    </span>
                  </button>
                  {expandedFAQ === faq.id && (
                    <div className={styles['faq-answer']}>
                      {faq.answer}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Support Section */}
          <div className={styles['support-section']}>
            <h3 className={styles['subsection-header']}>Need More Help?</h3>
            <div className={styles['support-grid']}>
              <button 
                className={styles['support-button']}
                onClick={() => handleSupportClick('Full Documentation')}
              >
                📖 Full Documentation
              </button>
              <button 
                className={styles['support-button']}
                onClick={() => handleSupportClick('Community Discussions')}
              >
                💬 Community Discussions
              </button>
              <button 
                className={styles['support-button']}
                onClick={() => handleSupportClick('Report a Bug')}
              >
                🐛 Report a Bug
              </button>
              <button 
                className={styles['support-button']}
                onClick={() => handleSupportClick('Contact Support')}
              >
                ✉️ Contact Support
              </button>
            </div>
          </div>

          {/* Pro Tips Section */}
          <div className={styles['tips-section']}>
            <h4 className={styles['subsection-header']}>Pro Tips</h4>
            <ul className={styles['tips-list']}>
              <li className={styles['tip-item']}>
                <strong>Keyboard Shortcuts:</strong> Use Ctrl+Enter to send messages quickly.
              </li>
              <li className={styles['tip-item']}>
                <strong>Contextual Help:</strong> Hover over elements for tooltips and more information.
              </li>
              <li className={styles['tip-item']}>
                <strong>Experiment:</strong> Try different models for varied response styles.
              </li>
              <li className={styles['tip-item']}>
                <strong>Save Conversations:</strong> Use chat history to revisit important discussions.
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HelpModal;
