/**
 * Settings Modal for V2 - BACKUP
 * Provides a comprehensive settings interface
 */

import React, { useState } from 'react';
import { useSettingsStore } from '../../stores/settingsStore';
import { modalActions } from '../../stores/modalStore';
import { ProviderConfig } from '../../stores/settingsStore';
import {
  CheckIcon,
  XIcon,
  InfoIcon,
  SettingsIcon,
  MessageSquareIcon,
  SearchIcon,
  RefreshIcon
} from '../ui';
import './ModalContent.module.css';

interface SettingsModalProps {
  onClose: () => void;
  onBack: () => void;
}

interface SettingSection {
  id: string;
  title: string;
  icon: React.ComponentType<any>;
  description: string;
}

const SETTING_SECTIONS: SettingSection[] = [
  {
    id: 'providers',
    title: 'AI Providers',
    icon: MessageSquareIcon,
    description: 'Manage AI provider configurations and API keys'
  },
  {
    id: 'models',
    title: 'Model Configuration',
    icon: SettingsIcon,
    description: 'Configure model settings and preferences'
  },
  {
    id: 'privacy',
    title: 'Privacy & Security',
    icon: InfoIcon,
    description: 'Data collection and security settings'
  },
  {
    id: 'advanced',
    title: 'Advanced Settings',
    icon: RefreshIcon,
    description: 'Developer options and advanced configurations'
  }
];

export const SettingsModal: React.FC<SettingsModalProps> = ({
  onClose,
  onBack
}) => {
  const { providers, currentProvider } = useSettingsStore();
  const [activeSection, setActiveSection] = useState('providers');

  const handleOpenProviderHealth = () => {
    modalActions.showProviderHealth();
  };

  const handleOpenModelConfiguration = () => {
    modalActions.showModelConfiguration();
  };

  const renderProvidersSection = () => (
    <div className="settings-section-content">
      <h3>AI Providers</h3>
      <p>Manage your AI provider configurations and monitor their health status.</p>
      
      <div className="settings-actions">
        <button 
          className="settings-action-button"
          onClick={handleOpenModelConfiguration}
        >
          <SettingsIcon size={16} />
          Configure Models
        </button>
        
        <button 
          className="settings-action-button"
          onClick={handleOpenProviderHealth}
        >
          <InfoIcon size={16} />
          View Health Status
        </button>
      </div>

      <div className="providers-summary">
        <h4>Current Configuration</h4>
        <div className="current-provider">
          <div className="provider-info">
            <span className="provider-label">Active Provider:</span>
            <span className="provider-value">
              {currentProvider?.name || 'None selected'}
            </span>
          </div>
          <div className="provider-info">
            <span className="provider-label">Status:</span>
            <span className={`provider-status ${currentProvider?.isConfigured ? 'configured' : 'not-configured'}`}>
              {currentProvider?.isConfigured ? (
                <>
                  <CheckIcon size={14} />
                  Configured
                </>
              ) : (
                <>
                  <XIcon size={14} />
                  Not Configured
                </>
              )}
            </span>
          </div>
        </div>

        <div className="providers-list">
          <h4>Available Providers ({providers.length})</h4>
          {providers.map((provider: ProviderConfig) => (
            <div key={provider.id} className="provider-item">
              <span className="provider-name">{provider.name}</span>
              <span className={`provider-badge ${provider.isConfigured ? 'configured' : 'not-configured'}`}>
                {provider.isConfigured ? 'Configured' : 'Not Configured'}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  const renderModelsSection = () => (
    <div className="settings-section-content">
      <h3>Model Configuration</h3>
      <p>Configure model-specific settings and preferences.</p>
      
      <div className="settings-group">
        <h4>Default Settings</h4>
        <div className="setting-item">
          <label>Temperature</label>
          <input 
            type="range" 
            min="0" 
            max="2" 
            step="0.1" 
            defaultValue="0.7"
            className="setting-slider"
          />
          <span className="setting-value">0.7</span>
        </div>
        
        <div className="setting-item">
          <label>Max Tokens</label>
          <input 
            type="number" 
            min="1" 
            max="8192" 
            defaultValue="2048"
            className="setting-input"
          />
        </div>
      </div>
    </div>
  );

  const renderPrivacySection = () => (
    <div className="settings-section-content">
      <h3>Privacy & Security</h3>
      <p>Control how your data is handled and stored.</p>
      
      <div className="settings-group">
        <div className="setting-item checkbox-setting">
          <input type="checkbox" id="data-collection" />
          <label htmlFor="data-collection">
            Enable usage analytics
            <span className="setting-description">
              Help improve SahAI by sharing anonymous usage data
            </span>
          </label>
        </div>
        
        <div className="setting-item checkbox-setting">
          <input type="checkbox" id="crash-reporting" defaultChecked />
          <label htmlFor="crash-reporting">
            Enable crash reporting
            <span className="setting-description">
              Automatically report crashes to help fix bugs
            </span>
          </label>
        </div>
        
        <div className="setting-item checkbox-setting">
          <input type="checkbox" id="auto-save" defaultChecked />
          <label htmlFor="auto-save">
            Auto-save conversations
            <span className="setting-description">
              Automatically save chat sessions locally
            </span>
          </label>
        </div>
      </div>
    </div>
  );

  const renderAdvancedSection = () => (
    <div className="settings-section-content">
      <h3>Advanced Settings</h3>
      <p>Developer options and advanced configurations.</p>
      
      <div className="settings-group">
        <div className="setting-item checkbox-setting">
          <input type="checkbox" id="debug-mode" />
          <label htmlFor="debug-mode">
            Enable debug mode
            <span className="setting-description">
              Show detailed logging and debug information
            </span>
          </label>
        </div>
        
        <div className="setting-item">
          <label>Request Timeout (seconds)</label>
          <input 
            type="number" 
            min="5" 
            max="300" 
            defaultValue="30"
            className="setting-input"
          />
        </div>
        
        <div className="setting-item">
          <label>Custom CSS</label>
          <textarea 
            placeholder="Enter custom CSS rules..."
            className="setting-textarea"
            rows={4}
          />
        </div>
      </div>
    </div>
  );

  const renderSectionContent = () => {
    switch (activeSection) {
      case 'providers':
        return renderProvidersSection();
      case 'models':
        return renderModelsSection();
      case 'privacy':
        return renderPrivacySection();
      case 'advanced':
        return renderAdvancedSection();
      default:
        return renderProvidersSection();
    }
  };

  return (
    <div className="modal-content">
      <div className="settings-modal">
        <div className="settings-sidebar">
          <h3>Settings</h3>
          <nav className="settings-nav">
            {SETTING_SECTIONS.map(section => {
              const IconComponent = section.icon;
              return (
                <button
                  key={section.id}
                  className={`settings-nav-item ${activeSection === section.id ? 'active' : ''}`}
                  onClick={() => setActiveSection(section.id)}
                >
                  <IconComponent size={16} />
                  <div className="nav-item-content">
                    <span className="nav-item-title">{section.title}</span>
                    <span className="nav-item-description">{section.description}</span>
                  </div>
                </button>
              );
            })}
          </nav>
        </div>
        
        <div className="settings-content">
          {renderSectionContent()}
        </div>
      </div>
    </div>
  );
};
