/**
 * About Modal for V2
 * Displays application information, version, features, and useful links
 * Inspired by About_modal_demo.html
 */

import React from 'react';
import styles from './ModalContent.module.css';

interface AboutModalProps {
  onClose: () => void;
  onBack: () => void;
}

export const AboutModal: React.FC<AboutModalProps> = ({ onClose, onBack }) => {
  const handleLinkClick = (linkType: string) => {
    console.log(`Opening: ${linkType}`);
    // In a real implementation, these would open actual links
    switch (linkType) {
      case 'Documentation':
        // window.open('https://docs.sahai.ai', '_blank');
        break;
      case 'GitHub Repository':
        // window.open('https://github.com/sahai-ai/cep-extension', '_blank');
        break;
      case 'Privacy Policy':
        // window.open('https://sahai.ai/privacy', '_blank');
        break;
      case 'Terms of Service':
        // window.open('https://sahai.ai/terms', '_blank');
        break;
      default:
        break;
    }
  };

  return (
    <div className={styles['modal-content']}>
      <div className={styles['modal-header']}>
        <h2 className={styles['modal-title']}>About SahAI Extension</h2>
      </div>
      
      <div className={styles['modal-body']}>
        <div className={styles['about-section']}>
          {/* Logo Section */}
          <div className={styles['logo-section']}>
            <div className={styles['app-icon']}>✨</div>
            <h2 className={styles['app-name']}>SahAI Extension</h2>
            <p className={styles['version']}>Version 2.0.0</p>
          </div>

          {/* Info Section */}
          <div className={styles['info-section']}>
            <h3 className={styles['section-header']}>About This Extension</h3>
            <p className={styles['section-description']}>
              SahAI is a powerful AI extension designed to enhance your creative workflow.
              It integrates seamlessly with Adobe Creative Suite to provide intelligent assistance
              for your creative projects.
            </p>

            {/* Features List */}
            <ul className={styles['features-list']}>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>🚀</span>
                <div>
                  <strong>Multi-Model Support:</strong> Access various AI models including GPT-4, Claude, and more.
                </div>
              </li>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>📊</span>
                <div>
                  <strong>Usage Analytics:</strong> Track your AI interactions and performance metrics.
                </div>
              </li>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>⚙️</span>
                <div>
                  <strong>Advanced Configuration:</strong> Tailor settings to your specific needs.
                </div>
              </li>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>📚</span>
                <div>
                  <strong>Comprehensive Help:</strong> Get started quickly with built-in guidance.
                </div>
              </li>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>🔒</span>
                <div>
                  <strong>Secure & Private:</strong> Your data stays secure with local encryption.
                </div>
              </li>
              <li className={styles['feature-item']}>
                <span className={styles['feature-icon']}>🎨</span>
                <div>
                  <strong>Creative Suite Integration:</strong> Works seamlessly with Adobe applications.
                </div>
              </li>
            </ul>
          </div>

          {/* Links Section */}
          <div className={styles['links-section']}>
            <h3 className={styles['section-header']}>Useful Links</h3>
            <div className={styles['link-grid']}>
              <button 
                className={styles['link-button']}
                onClick={() => handleLinkClick('Documentation')}
              >
                📖 Documentation
              </button>
              <button 
                className={styles['link-button']}
                onClick={() => handleLinkClick('GitHub Repository')}
              >
                🔗 GitHub Repository
              </button>
              <button 
                className={styles['link-button']}
                onClick={() => handleLinkClick('Privacy Policy')}
              >
                🔒 Privacy Policy
              </button>
              <button 
                className={styles['link-button']}
                onClick={() => handleLinkClick('Terms of Service')}
              >
                📋 Terms of Service
              </button>
            </div>
          </div>

          {/* Copyright Section */}
          <div className={styles['copyright-section']}>
            <p className={styles['copyright']}>© 2025 SahAI Extension. All rights reserved.</p>
            <p className={styles['disclaimer']}>
              This software is provided "as is," without warranty of any kind.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AboutModal;
